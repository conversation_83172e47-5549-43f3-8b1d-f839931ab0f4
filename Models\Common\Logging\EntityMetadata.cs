using System;
using System.Collections.Generic;
using System.Reflection;

namespace FAST_ERP_Backend.Models.Common.Logging
{
    /// <summary>
    /// 實體元數據 - 用於快取實體類型的反射資訊
    /// </summary>
    public class EntityMetadata
    {
        /// <summary> 實體類型 </summary>
        public Type EntityType { get; set; }
        
        /// <summary> 安全屬性列表（可序列化的屬性） </summary>
        public List<PropertyInfo> SafeProperties { get; set; } = new List<PropertyInfo>();
        
        /// <summary> 主鍵屬性 </summary>
        public PropertyInfo? PrimaryKeyProperty { get; set; }
        
        /// <summary> 使用者ID屬性 </summary>
        public PropertyInfo? UserIdProperty { get; set; }
        
        /// <summary> 導航屬性列表 </summary>
        public List<PropertyInfo> NavigationProperties { get; set; } = new List<PropertyInfo>();
        
        /// <summary> 建立時間 </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        
        /// <summary> 建構式 </summary>
        public EntityMetadata()
        {
            EntityType = typeof(object);
            SafeProperties = new List<PropertyInfo>();
            NavigationProperties = new List<PropertyInfo>();
            CreatedAt = DateTime.UtcNow;
        }
        
        /// <summary> 建構式 </summary>
        /// <param name="entityType">實體類型</param>
        public EntityMetadata(Type entityType)
        {
            EntityType = entityType;
            SafeProperties = new List<PropertyInfo>();
            NavigationProperties = new List<PropertyInfo>();
            CreatedAt = DateTime.UtcNow;
        }
    }
}
