using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using FAST_ERP_Backend.Interfaces.Common;
using FAST_ERP_Backend.Models.Common.Logging;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace FAST_ERP_Backend.Controllers.Common
{
    /// <summary>
    /// 日誌系統遷移控制器
    /// 提供遷移過程中的測試、驗證和監控功能
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class LoggingMigrationController : ControllerBase
    {
        #region Private Fields

        private readonly IResilientLoggerService _resilientLogger;
        private readonly ILoggerService _legacyLogger;
        private readonly IMongoDBConnectionManager _connectionManager;
        private readonly ILogDataProcessor _dataProcessor;
        private readonly ILogger<LoggingMigrationController> _logger;
        private readonly IConfiguration _configuration;

        #endregion

        #region Constructor

        /// <summary>
        /// 初始化遷移控制器
        /// </summary>
        public LoggingMigrationController(
            IResilientLoggerService resilientLogger,
            ILoggerService legacyLogger,
            IMongoDBConnectionManager connectionManager,
            ILogDataProcessor dataProcessor,
            ILogger<LoggingMigrationController> logger,
            IConfiguration configuration)
        {
            _resilientLogger = resilientLogger ?? throw new ArgumentNullException(nameof(resilientLogger));
            _legacyLogger = legacyLogger ?? throw new ArgumentNullException(nameof(legacyLogger));
            _connectionManager = connectionManager ?? throw new ArgumentNullException(nameof(connectionManager));
            _dataProcessor = dataProcessor ?? throw new ArgumentNullException(nameof(dataProcessor));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        }

        #endregion

        #region Health Check Endpoints

        /// <summary> 獲取新日誌系統健康狀態 </summary>
        [HttpGet("health/new-system")]
        public async Task<IActionResult> GetNewSystemHealth()
        {
            try
            {
                var healthStatus = await _resilientLogger.GetHealthStatusAsync();
                var connectionStatus = await _connectionManager.GetConnectionStatusAsync();

                var result = new
                {
                    Timestamp = DateTime.UtcNow,
                    SystemHealth = healthStatus,
                    ConnectionStatus = connectionStatus,
                    OverallStatus = healthStatus.IsHealthy && connectionStatus.IsConnected ? "Healthy" : "Unhealthy"
                };

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "獲取新系統健康狀態時發生錯誤");
                return StatusCode(500, new { Error = ex.Message });
            }
        }

        /// <summary> 獲取錯誤統計資訊 </summary>
        [HttpGet("health/error-statistics")]
        public async Task<IActionResult> GetErrorStatistics()
        {
            try
            {
                var statistics = await _resilientLogger.GetErrorStatisticsAsync();
                return Ok(statistics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "獲取錯誤統計時發生錯誤");
                return StatusCode(500, new { Error = ex.Message });
            }
        }

        /// <summary> 重置錯誤統計 </summary>
        [HttpPost("health/reset-statistics")]
        public async Task<IActionResult> ResetErrorStatistics()
        {
            try
            {
                await _resilientLogger.ClearErrorStatisticsAsync();
                return Ok(new { Message = "錯誤統計已重置", Timestamp = DateTime.UtcNow });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "重置錯誤統計時發生錯誤");
                return StatusCode(500, new { Error = ex.Message });
            }
        }

        #endregion

        #region Testing Endpoints

        /// <summary> 測試新日誌系統基本功能 </summary>
        [HttpPost("test/basic-functionality")]
        public async Task<IActionResult> TestBasicFunctionality()
        {
            var testResults = new List<object>();
            var overallSuccess = true;

            try
            {
                // 測試 1: 基本資訊日誌
                var stopwatch = Stopwatch.StartNew();
                var success1 = await _resilientLogger.TryLogAsync(
                    new SimpleLogEntry(LogLevels.Information, "遷移測試 - 基本資訊日誌", "MigrationTest"));
                stopwatch.Stop();

                testResults.Add(new
                {
                    Test = "基本資訊日誌",
                    Success = success1,
                    ResponseTime = stopwatch.ElapsedMilliseconds,
                    Status = success1 ? "PASS" : "FAIL"
                });

                if (!success1) overallSuccess = false;

                // 測試 2: 錯誤日誌
                stopwatch.Restart();
                await _resilientLogger.LogErrorAsync("遷移測試 - 錯誤日誌", 
                    new Exception("測試例外"), "MigrationTest");
                stopwatch.Stop();

                testResults.Add(new
                {
                    Test = "錯誤日誌",
                    Success = true, // 錯誤日誌不會返回失敗
                    ResponseTime = stopwatch.ElapsedMilliseconds,
                    Status = "PASS"
                });

                // 測試 3: 資料日誌
                stopwatch.Restart();
                var testData = new { TestId = Guid.NewGuid(), TestName = "遷移測試資料", TestTime = DateTime.UtcNow };
                await _resilientLogger.LogDataAsync("遷移測試 - 資料日誌", testData, 
                    Guid.NewGuid().ToString(), "MigrationTest");
                stopwatch.Stop();

                testResults.Add(new
                {
                    Test = "資料日誌",
                    Success = true,
                    ResponseTime = stopwatch.ElapsedMilliseconds,
                    Status = "PASS"
                });

                return Ok(new
                {
                    OverallResult = overallSuccess ? "PASS" : "FAIL",
                    TestResults = testResults,
                    Timestamp = DateTime.UtcNow,
                    Summary = new
                    {
                        TotalTests = testResults.Count,
                        PassedTests = testResults.Count(r => r.GetType().GetProperty("Status")?.GetValue(r)?.ToString() == "PASS"),
                        AverageResponseTime = testResults.Average(r => (long)r.GetType().GetProperty("ResponseTime")?.GetValue(r)!)
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "基本功能測試時發生錯誤");
                return StatusCode(500, new { Error = ex.Message, TestResults = testResults });
            }
        }

        /// <summary> 測試效能 </summary>
        [HttpPost("test/performance")]
        public async Task<IActionResult> TestPerformance([FromQuery] int logCount = 100)
        {
            try
            {
                if (logCount > 1000) logCount = 1000; // 限制測試規模

                var stopwatch = Stopwatch.StartNew();
                var tasks = new List<Task<bool>>();

                // 並發測試
                for (int i = 0; i < logCount; i++)
                {
                    var entry = new SimpleLogEntry(LogLevels.Information, 
                        $"效能測試日誌 {i}", "PerformanceTest");
                    tasks.Add(_resilientLogger.TryLogAsync(entry));
                }

                var results = await Task.WhenAll(tasks);
                stopwatch.Stop();

                var successCount = results.Count(r => r);
                var successRate = (double)successCount / logCount * 100;

                return Ok(new
                {
                    TestType = "效能測試",
                    LogCount = logCount,
                    SuccessCount = successCount,
                    SuccessRate = Math.Round(successRate, 2),
                    TotalTime = stopwatch.ElapsedMilliseconds,
                    AverageTimePerLog = Math.Round((double)stopwatch.ElapsedMilliseconds / logCount, 2),
                    ThroughputPerSecond = Math.Round(logCount / (stopwatch.ElapsedMilliseconds / 1000.0), 2),
                    Status = successRate >= 95 ? "PASS" : "FAIL",
                    Timestamp = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "效能測試時發生錯誤");
                return StatusCode(500, new { Error = ex.Message });
            }
        }

        /// <summary> 測試錯誤處理和熔斷器 </summary>
        [HttpPost("test/error-handling")]
        public async Task<IActionResult> TestErrorHandling()
        {
            try
            {
                var testResults = new List<object>();

                // 測試 1: 正常操作
                var normalSuccess = await _resilientLogger.TryLogAsync(
                    new SimpleLogEntry(LogLevels.Information, "錯誤處理測試 - 正常操作", "ErrorHandlingTest"));

                testResults.Add(new
                {
                    Test = "正常操作",
                    Success = normalSuccess,
                    Status = normalSuccess ? "PASS" : "FAIL"
                });

                // 測試 2: 獲取熔斷器狀態
                var healthStatus = await _resilientLogger.GetHealthStatusAsync();
                testResults.Add(new
                {
                    Test = "熔斷器狀態檢查",
                    CircuitBreakerState = healthStatus.CircuitBreakerState.ToString(),
                    IsHealthy = healthStatus.IsHealthy,
                    Status = "INFO"
                });

                // 測試 3: 降級模式測試
                _resilientLogger.SetFallbackMode(true);
                var fallbackSuccess = await _resilientLogger.TryLogAsync(
                    new SimpleLogEntry(LogLevels.Warning, "錯誤處理測試 - 降級模式", "ErrorHandlingTest"));
                _resilientLogger.SetFallbackMode(false);

                testResults.Add(new
                {
                    Test = "降級模式",
                    Success = fallbackSuccess,
                    Status = "PASS" // 降級模式下仍應能處理
                });

                return Ok(new
                {
                    TestType = "錯誤處理測試",
                    TestResults = testResults,
                    HealthStatus = healthStatus,
                    Timestamp = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "錯誤處理測試時發生錯誤");
                return StatusCode(500, new { Error = ex.Message });
            }
        }

        #endregion

        #region Comparison Endpoints

        /// <summary> 比較新舊系統效能 </summary>
        [HttpPost("compare/performance")]
        public async Task<IActionResult> ComparePerformance([FromQuery] int testCount = 50)
        {
            try
            {
                if (testCount > 200) testCount = 200; // 限制測試規模

                var results = new
                {
                    NewSystem = await TestSystemPerformance(_resilientLogger, testCount, "新系統"),
                    LegacySystem = await TestSystemPerformance(_legacyLogger, testCount, "舊系統"),
                    Timestamp = DateTime.UtcNow
                };

                return Ok(results);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "效能比較測試時發生錯誤");
                return StatusCode(500, new { Error = ex.Message });
            }
        }

        #endregion

        #region Circuit Breaker Control

        /// <summary> 重置熔斷器 </summary>
        [HttpPost("circuit-breaker/reset")]
        public async Task<IActionResult> ResetCircuitBreaker()
        {
            try
            {
                await _resilientLogger.ResetCircuitBreakerAsync();
                return Ok(new { Message = "熔斷器已重置", Timestamp = DateTime.UtcNow });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "重置熔斷器時發生錯誤");
                return StatusCode(500, new { Error = ex.Message });
            }
        }

        /// <summary> 啟用/停用熔斷器 </summary>
        [HttpPost("circuit-breaker/toggle")]
        public async Task<IActionResult> ToggleCircuitBreaker([FromQuery] bool enabled = true)
        {
            try
            {
                _resilientLogger.EnableCircuitBreaker(enabled);
                var status = await _resilientLogger.GetHealthStatusAsync();
                
                return Ok(new
                {
                    Message = $"熔斷器已{(enabled ? "啟用" : "停用")}",
                    CurrentState = status.CircuitBreakerState.ToString(),
                    Timestamp = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "切換熔斷器狀態時發生錯誤");
                return StatusCode(500, new { Error = ex.Message });
            }
        }

        #endregion

        #region Private Methods

        /// <summary> 測試系統效能 </summary>
        private async Task<object> TestSystemPerformance(ILoggerService loggerService, int testCount, string systemName)
        {
            var stopwatch = Stopwatch.StartNew();
            var successCount = 0;
            var errorCount = 0;

            for (int i = 0; i < testCount; i++)
            {
                try
                {
                    await loggerService.LogInfoAsync($"{systemName}效能測試 {i}", "PerformanceComparison");
                    successCount++;
                }
                catch
                {
                    errorCount++;
                }
            }

            stopwatch.Stop();

            return new
            {
                SystemName = systemName,
                TestCount = testCount,
                SuccessCount = successCount,
                ErrorCount = errorCount,
                SuccessRate = Math.Round((double)successCount / testCount * 100, 2),
                TotalTime = stopwatch.ElapsedMilliseconds,
                AverageTimePerLog = Math.Round((double)stopwatch.ElapsedMilliseconds / testCount, 2),
                ThroughputPerSecond = Math.Round(testCount / (stopwatch.ElapsedMilliseconds / 1000.0), 2)
            };
        }

        #endregion
    }
}
