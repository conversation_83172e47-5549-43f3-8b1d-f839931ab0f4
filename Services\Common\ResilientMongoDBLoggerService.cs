using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using FAST_ERP_Backend.Interfaces.Common;
using FAST_ERP_Backend.Models.Common.Logging;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace FAST_ERP_Backend.Services.Common
{
    /// <summary>
    /// 具有彈性和錯誤處理能力的 MongoDB 日誌服務
    /// 實現重試機制、熔斷器模式、降級處理和完整的錯誤隔離
    /// </summary>
    public class ResilientMongoDBLoggerService : IResilientLoggerService, IDisposable
    {
        #region Private Fields

        private readonly IMongoDBConnectionManager _connectionManager;
        private readonly ILogDataProcessor _dataProcessor;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly ILogger<ResilientMongoDBLoggerService> _logger;
        private readonly IConfiguration _configuration;

        // 錯誤處理和統計
        private readonly LoggingErrorStatistics _errorStatistics;
        private readonly ConcurrentQueue<SimpleLogEntry> _fallbackQueue;
        private readonly Timer _healthCheckTimer;
        private readonly Timer _statisticsTimer;

        // 熔斷器狀態
        private CircuitBreakerState _circuitBreakerState;
        private DateTime _circuitBreakerLastStateChange;
        private int _consecutiveFailures;
        private int _consecutiveSuccesses;

        // 配置選項
        private readonly RetryOptions _retryOptions;
        private readonly CircuitBreakerOptions _circuitBreakerOptions;
        private readonly string _collectionName;

        // 服務狀態
        private readonly LoggingHealthStatus _healthStatus;
        private bool _fallbackMode;
        private bool _disposed;
        private readonly object _lockObject = new object();

        #endregion

        #region Constructor

        /// <summary>
        /// 初始化彈性 MongoDB 日誌服務
        /// </summary>
        public ResilientMongoDBLoggerService(
            IMongoDBConnectionManager connectionManager,
            ILogDataProcessor dataProcessor,
            IHttpContextAccessor httpContextAccessor,
            ILogger<ResilientMongoDBLoggerService> logger,
            IConfiguration configuration)
        {
            _connectionManager = connectionManager ?? throw new ArgumentNullException(nameof(connectionManager));
            _dataProcessor = dataProcessor ?? throw new ArgumentNullException(nameof(dataProcessor));
            _httpContextAccessor = httpContextAccessor ?? throw new ArgumentNullException(nameof(httpContextAccessor));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));

            // 讀取配置
            _collectionName = _configuration["MongoDB:CollectionName"] ?? "Logger";

            // 初始化選項
            _retryOptions = new RetryOptions();
            _circuitBreakerOptions = new CircuitBreakerOptions();
            
            // 初始化狀態
            _errorStatistics = new LoggingErrorStatistics { LastResetTime = DateTime.UtcNow };
            _fallbackQueue = new ConcurrentQueue<SimpleLogEntry>();
            _circuitBreakerState = CircuitBreakerState.Closed;
            _circuitBreakerLastStateChange = DateTime.UtcNow;
            
            _healthStatus = new LoggingHealthStatus
            {
                ServiceStartTime = DateTime.UtcNow,
                IsHealthy = true,
                CircuitBreakerState = _circuitBreakerState
            };

            // 啟動定時器
            _healthCheckTimer = new Timer(PerformHealthCheck, null, TimeSpan.FromMinutes(1), TimeSpan.FromMinutes(1));
            _statisticsTimer = new Timer(UpdateStatistics, null, TimeSpan.FromMinutes(5), TimeSpan.FromMinutes(5));

            _logger.LogInformation("彈性MongoDB日誌服務已初始化");
        }

        #endregion

        #region ILoggerService Implementation

        /// <summary> 記錄調試日誌 </summary>
        public async Task LogDebugAsync(string message, string source = "System")
        {
            var entry = _dataProcessor.ProcessSimpleLog(LogLevels.Debug, message, source);
            await TryLogAsync(entry);
        }

        /// <summary> 記錄信息日誌 </summary>
        public async Task LogInfoAsync(string message, string source = "System")
        {
            var entry = _dataProcessor.ProcessSimpleLog(LogLevels.Information, message, source);
            await TryLogAsync(entry);
        }

        /// <summary> 記錄警告日誌 </summary>
        public async Task LogWarningAsync(string message, string source = "System")
        {
            var entry = _dataProcessor.ProcessSimpleLog(LogLevels.Warning, message, source);
            await TryLogAsync(entry);
        }

        /// <summary> 記錄錯誤日誌 </summary>
        public async Task LogErrorAsync(string message, Exception? exception = null, string source = "System")
        {
            var entry = _dataProcessor.ProcessSimpleLog(LogLevels.Error, message, source, exception);
            await TryLogAsync(entry);
        }

        /// <summary> 記錄異動日誌 </summary>
        public async Task LogDataAsync<T>(string message, T changedData, string transactionId, string source = "System") where T : class
        {
            try
            {
                var entry = _dataProcessor.ProcessSimpleLog(LogLevels.Information, message, source);
                entry.TransactionId = transactionId;
                
                if (changedData != null)
                {
                    entry.Data = new LogData
                    {
                        Operation = OperationTypes.System,
                        AfterData = _dataProcessor.SafeSerialize(changedData),
                        Summary = message
                    };
                }

                await TryLogAsync(entry);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "記錄異動日誌時發生錯誤");
                await HandleLoggingError(ex, "LogDataAsync");
            }
        }

        #endregion

        #region IResilientLoggerService Implementation

        /// <summary> 嘗試記錄日誌（帶重試機制） </summary>
        public async Task<bool> TryLogAsync(SimpleLogEntry entry, int maxRetries = 3)
        {
            if (_disposed) return false;

            // 檢查熔斷器狀態
            if (!CanExecute())
            {
                await HandleFallback(entry);
                return false;
            }

            var retryCount = 0;
            var delay = _retryOptions.BaseDelayMs;

            while (retryCount <= maxRetries)
            {
                try
                {
                    var startTime = DateTime.UtcNow;
                    
                    // 填充上下文資訊
                    PopulateContextInfo(entry);

                    // 獲取 MongoDB 集合並插入
                    var collection = await _connectionManager.GetCollectionAsync<SimpleLogEntry>(_collectionName);
                    await collection.InsertOneAsync(entry);

                    // 記錄成功
                    await RecordSuccess(DateTime.UtcNow - startTime);
                    return true;
                }
                catch (Exception ex)
                {
                    retryCount++;
                    await RecordFailure(ex);

                    if (retryCount > maxRetries)
                    {
                        _logger.LogError(ex, "日誌記錄失敗，已達最大重試次數: {MaxRetries}", maxRetries);
                        await HandleFallback(entry);
                        return false;
                    }

                    // 計算延遲時間
                    if (_retryOptions.UseExponentialBackoff)
                    {
                        delay = Math.Min((int)(delay * _retryOptions.DelayMultiplier), _retryOptions.MaxDelayMs);
                    }

                    if (_retryOptions.UseJitter)
                    {
                        var jitter = new Random().Next(0, delay / 4);
                        delay += jitter;
                    }

                    _logger.LogWarning("日誌記錄失敗，{RetryCount}/{MaxRetries} 次重試，{Delay}ms 後重試: {Error}", 
                        retryCount, maxRetries, delay, ex.Message);

                    await Task.Delay(delay);
                }
            }

            return false;
        }

        /// <summary> 批次記錄日誌 </summary>
        public async Task<int> TryLogBatchAsync(SimpleLogEntry[] entries, int maxRetries = 3)
        {
            if (_disposed || entries == null || entries.Length == 0) return 0;

            var successCount = 0;
            var tasks = entries.Select(entry => TryLogAsync(entry, maxRetries));
            var results = await Task.WhenAll(tasks);

            successCount = results.Count(r => r);
            
            _logger.LogDebug("批次日誌記錄完成: {SuccessCount}/{TotalCount}", successCount, entries.Length);
            return successCount;
        }

        /// <summary> 獲取日誌服務健康狀態 </summary>
        public async Task<LoggingHealthStatus> GetHealthStatusAsync()
        {
            try
            {
                var connectionStatus = await _connectionManager.GetConnectionStatusAsync();
                
                lock (_lockObject)
                {
                    _healthStatus.IsMongoDBConnected = connectionStatus.IsConnected;
                    _healthStatus.IsHealthy = connectionStatus.IsConnected && 
                                            _circuitBreakerState != CircuitBreakerState.Open;
                    _healthStatus.CircuitBreakerState = _circuitBreakerState;
                    _healthStatus.IsFallbackMode = _fallbackMode;
                    _healthStatus.PendingLogCount = _fallbackQueue.Count;
                    
                    // 計算成功率
                    var totalLogs = _healthStatus.TotalProcessedLogs;
                    if (totalLogs > 0)
                    {
                        _healthStatus.SuccessRate = ((double)(totalLogs - _errorStatistics.TotalErrors) / totalLogs) * 100;
                    }
                }

                return _healthStatus;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "獲取健康狀態時發生錯誤");
                return _healthStatus;
            }
        }

        /// <summary> 啟用或停用熔斷器 </summary>
        public void EnableCircuitBreaker(bool enabled)
        {
            lock (_lockObject)
            {
                _circuitBreakerOptions.Enabled = enabled;
                if (!enabled)
                {
                    _circuitBreakerState = CircuitBreakerState.Disabled;
                    _consecutiveFailures = 0;
                    _consecutiveSuccesses = 0;
                }
                else if (_circuitBreakerState == CircuitBreakerState.Disabled)
                {
                    _circuitBreakerState = CircuitBreakerState.Closed;
                }
                
                _circuitBreakerLastStateChange = DateTime.UtcNow;
                _healthStatus.CircuitBreakerState = _circuitBreakerState;
            }

            _logger.LogInformation("熔斷器已{Status}", enabled ? "啟用" : "停用");
        }

        /// <summary> 重置熔斷器狀態 </summary>
        public async Task ResetCircuitBreakerAsync()
        {
            lock (_lockObject)
            {
                _circuitBreakerState = CircuitBreakerState.Closed;
                _consecutiveFailures = 0;
                _consecutiveSuccesses = 0;
                _circuitBreakerLastStateChange = DateTime.UtcNow;
                _healthStatus.CircuitBreakerState = _circuitBreakerState;
            }

            _logger.LogInformation("熔斷器狀態已重置");
            await Task.CompletedTask;
        }

        /// <summary> 獲取錯誤統計資訊 </summary>
        public async Task<LoggingErrorStatistics> GetErrorStatisticsAsync()
        {
            lock (_lockObject)
            {
                // 計算時間窗口內的錯誤
                var now = DateTime.UtcNow;
                _errorStatistics.ErrorsLast24Hours = CalculateErrorsInTimeWindow(TimeSpan.FromHours(24));
                _errorStatistics.ErrorsLastHour = CalculateErrorsInTimeWindow(TimeSpan.FromHours(1));
                
                // 計算錯誤率
                var totalLogs = _healthStatus.TotalProcessedLogs;
                if (totalLogs > 0)
                {
                    _errorStatistics.ErrorRate = ((double)_errorStatistics.TotalErrors / totalLogs) * 100;
                }

                return _errorStatistics;
            }
        }

        /// <summary> 清除錯誤統計 </summary>
        public async Task ClearErrorStatisticsAsync()
        {
            lock (_lockObject)
            {
                _errorStatistics.TotalErrors = 0;
                _errorStatistics.MongoDBConnectionErrors = 0;
                _errorStatistics.SerializationErrors = 0;
                _errorStatistics.TimeoutErrors = 0;
                _errorStatistics.OtherErrors = 0;
                _errorStatistics.ConsecutiveErrors = 0;
                _errorStatistics.LastResetTime = DateTime.UtcNow;
                _errorStatistics.ErrorTypeStatistics.Clear();
                
                _consecutiveFailures = 0;
                _healthStatus.LastErrorTime = DateTime.MinValue;
                _healthStatus.LastErrorMessage = null;
            }

            _logger.LogInformation("錯誤統計已清除");
            await Task.CompletedTask;
        }

        /// <summary> 設定降級模式 </summary>
        public void SetFallbackMode(bool enabled)
        {
            lock (_lockObject)
            {
                _fallbackMode = enabled;
                _healthStatus.IsFallbackMode = enabled;
            }

            _logger.LogInformation("降級模式已{Status}", enabled ? "啟用" : "停用");
        }

        #endregion

        #region Private Methods

        /// <summary> 檢查是否可以執行操作 </summary>
        private bool CanExecute()
        {
            if (!_circuitBreakerOptions.Enabled) return true;

            lock (_lockObject)
            {
                switch (_circuitBreakerState)
                {
                    case CircuitBreakerState.Closed:
                        return true;

                    case CircuitBreakerState.Open:
                        // 檢查是否可以嘗試半開啟
                        if (DateTime.UtcNow - _circuitBreakerLastStateChange > TimeSpan.FromMilliseconds(_circuitBreakerOptions.TimeoutMs))
                        {
                            _circuitBreakerState = CircuitBreakerState.HalfOpen;
                            _circuitBreakerLastStateChange = DateTime.UtcNow;
                            _consecutiveSuccesses = 0;
                            _logger.LogInformation("熔斷器進入半開啟狀態");
                            return true;
                        }
                        return false;

                    case CircuitBreakerState.HalfOpen:
                        return true;

                    case CircuitBreakerState.Disabled:
                        return true;

                    default:
                        return false;
                }
            }
        }

        /// <summary> 記錄成功操作 </summary>
        private async Task RecordSuccess(TimeSpan duration)
        {
            lock (_lockObject)
            {
                _consecutiveFailures = 0;
                _consecutiveSuccesses++;
                _healthStatus.TotalProcessedLogs++;
                _healthStatus.LastSuccessfulLog = DateTime.UtcNow;

                // 更新平均回應時間
                var currentAvg = _healthStatus.AverageResponseTimeMs;
                var totalLogs = _healthStatus.TotalProcessedLogs;
                _healthStatus.AverageResponseTimeMs = ((currentAvg * (totalLogs - 1)) + duration.TotalMilliseconds) / totalLogs;

                // 熔斷器狀態管理
                if (_circuitBreakerState == CircuitBreakerState.HalfOpen &&
                    _consecutiveSuccesses >= _circuitBreakerOptions.SuccessThreshold)
                {
                    _circuitBreakerState = CircuitBreakerState.Closed;
                    _circuitBreakerLastStateChange = DateTime.UtcNow;
                    _logger.LogInformation("熔斷器已關閉，服務恢復正常");
                }
            }

            await Task.CompletedTask;
        }

        /// <summary> 記錄失敗操作 </summary>
        private async Task RecordFailure(Exception exception)
        {
            lock (_lockObject)
            {
                _consecutiveFailures++;
                _consecutiveSuccesses = 0;
                _errorStatistics.TotalErrors++;
                _errorStatistics.ConsecutiveErrors = _consecutiveFailures;
                _healthStatus.LastErrorTime = DateTime.UtcNow;
                _healthStatus.LastErrorMessage = exception.Message;

                // 分類錯誤類型
                var errorType = ClassifyError(exception);
                switch (errorType)
                {
                    case "MongoDB":
                        _errorStatistics.MongoDBConnectionErrors++;
                        break;
                    case "Serialization":
                        _errorStatistics.SerializationErrors++;
                        break;
                    case "Timeout":
                        _errorStatistics.TimeoutErrors++;
                        break;
                    default:
                        _errorStatistics.OtherErrors++;
                        break;
                }

                // 更新錯誤類型統計
                if (!_errorStatistics.ErrorTypeStatistics.ContainsKey(errorType))
                {
                    _errorStatistics.ErrorTypeStatistics[errorType] = 0;
                }
                _errorStatistics.ErrorTypeStatistics[errorType]++;

                // 熔斷器狀態管理
                if (_circuitBreakerOptions.Enabled &&
                    _consecutiveFailures >= _circuitBreakerOptions.FailureThreshold &&
                    _circuitBreakerState != CircuitBreakerState.Open)
                {
                    _circuitBreakerState = CircuitBreakerState.Open;
                    _circuitBreakerLastStateChange = DateTime.UtcNow;
                    _logger.LogWarning("熔斷器已開啟，連續失敗次數: {ConsecutiveFailures}", _consecutiveFailures);
                }
            }

            await Task.CompletedTask;
        }

        /// <summary> 處理降級邏輯 </summary>
        private async Task HandleFallback(SimpleLogEntry entry)
        {
            try
            {
                // 將日誌加入降級佇列
                _fallbackQueue.Enqueue(entry);

                // 限制佇列大小
                const int maxQueueSize = 10000;
                while (_fallbackQueue.Count > maxQueueSize)
                {
                    _fallbackQueue.TryDequeue(out _);
                }

                // 記錄到系統日誌作為備份
                _logger.LogWarning("日誌記錄降級處理: {Message}", entry.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "降級處理失敗");
            }

            await Task.CompletedTask;
        }

        /// <summary> 填充上下文資訊 </summary>
        private void PopulateContextInfo(SimpleLogEntry entry)
        {
            try
            {
                var httpContext = _httpContextAccessor.HttpContext;
                if (httpContext != null)
                {
                    entry.IpAddress = httpContext.Connection.RemoteIpAddress?.ToString();
                    entry.RequestUrl = $"{httpContext.Request.Scheme}://{httpContext.Request.Host}{httpContext.Request.Path}";
                    entry.UserAgent = httpContext.Request.Headers["User-Agent"].FirstOrDefault();

                    // 嘗試從 Claims 中獲取使用者 ID
                    if (httpContext.User?.Identity?.IsAuthenticated == true)
                    {
                        entry.UserId = httpContext.User.FindFirst("sub")?.Value ??
                                      httpContext.User.FindFirst("userId")?.Value ??
                                      httpContext.User.Identity.Name;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogDebug(ex, "填充上下文資訊時發生錯誤");
            }
        }

        /// <summary> 分類錯誤類型 </summary>
        private string ClassifyError(Exception exception)
        {
            var exceptionType = exception.GetType().Name.ToLower();
            var message = exception.Message.ToLower();

            if (exceptionType.Contains("mongo") || message.Contains("mongo") || message.Contains("connection"))
                return "MongoDB";

            if (exceptionType.Contains("json") || exceptionType.Contains("serializ") || message.Contains("serializ"))
                return "Serialization";

            if (exceptionType.Contains("timeout") || message.Contains("timeout"))
                return "Timeout";

            return exception.GetType().Name;
        }

        /// <summary> 計算時間窗口內的錯誤數量 </summary>
        private long CalculateErrorsInTimeWindow(TimeSpan timeWindow)
        {
            // 這裡應該實現基於時間窗口的錯誤計算
            // 為簡化實現，暫時返回總錯誤數的估算值
            var now = DateTime.UtcNow;
            var windowStart = now - timeWindow;

            if (_healthStatus.LastErrorTime >= windowStart)
            {
                return Math.Min(_errorStatistics.TotalErrors, _errorStatistics.ConsecutiveErrors);
            }

            return 0;
        }

        /// <summary> 處理日誌錯誤 </summary>
        private async Task HandleLoggingError(Exception exception, string operation)
        {
            await RecordFailure(exception);

            // 創建錯誤日誌記錄
            var errorEntry = new SimpleLogEntry(LogLevels.Error,
                $"日誌服務內部錯誤 ({operation}): {exception.Message}", "LoggingService")
            {
                ErrorMessage = exception.Message,
                StackTrace = exception.StackTrace
            };

            await HandleFallback(errorEntry);
        }

        /// <summary> 執行健康檢查 </summary>
        private async void PerformHealthCheck(object? state)
        {
            try
            {
                var isHealthy = await _connectionManager.HealthCheckAsync();

                lock (_lockObject)
                {
                    _healthStatus.IsMongoDBConnected = isHealthy;
                    _healthStatus.IsHealthy = isHealthy && _circuitBreakerState != CircuitBreakerState.Open;
                }

                if (!isHealthy)
                {
                    _logger.LogWarning("MongoDB 健康檢查失敗");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "健康檢查時發生錯誤");
            }
        }

        /// <summary> 更新統計資訊 </summary>
        private async void UpdateStatistics(object? state)
        {
            try
            {
                await GetErrorStatisticsAsync();
                _logger.LogDebug("統計資訊已更新");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新統計資訊時發生錯誤");
            }
        }

        #endregion

        #region IDisposable Implementation

        /// <summary> 釋放資源 </summary>
        public void Dispose()
        {
            if (!_disposed)
            {
                _healthCheckTimer?.Dispose();
                _statisticsTimer?.Dispose();
                _disposed = true;
                _logger.LogDebug("彈性MongoDB日誌服務已釋放資源");
            }
        }

        #endregion
    }
}
