using System;
using System.Diagnostics;
using System.Threading;
using System.Threading.Tasks;
using FAST_ERP_Backend.Interfaces.Common;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using MongoDB.Bson;
using MongoDB.Driver;

namespace FAST_ERP_Backend.Services.Common
{
    /// <summary>
    /// MongoDB 連接管理器實現
    /// 提供連接池管理、健康檢查、重連機制和錯誤處理
    /// </summary>
    public class MongoDBConnectionManager : IMongoDBConnectionManager, IDisposable
    {
        #region Private Fields
        
        private readonly IConfiguration _configuration;
        private readonly ILogger<MongoDBConnectionManager> _logger;
        private readonly SemaphoreSlim _connectionSemaphore;
        private readonly object _lockObject = new object();
        
        private MongoClient? _mongoClient;
        private IMongoDatabase? _database;
        private MongoDBConnectionStatus _connectionStatus;
        private DateTime _lastHealthCheck;
        private bool _disposed = false;
        
        // 連接配置
        private readonly string _connectionString;
        private readonly string _databaseName;
        private readonly TimeSpan _healthCheckInterval = TimeSpan.FromMinutes(1);
        private readonly TimeSpan _connectionTimeout = TimeSpan.FromSeconds(30);
        
        #endregion

        #region Constructor

        /// <summary>
        /// 初始化 MongoDB 連接管理器
        /// </summary>
        /// <param name="configuration">配置服務</param>
        /// <param name="logger">日誌服務</param>
        public MongoDBConnectionManager(IConfiguration configuration, ILogger<MongoDBConnectionManager> logger)
        {
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _connectionSemaphore = new SemaphoreSlim(1, 1);

            // 讀取配置
            _connectionString = _configuration["MongoDB:ConnectionString"] 
                ?? throw new InvalidOperationException("MongoDB連接字符串未配置");
            _databaseName = _configuration["MongoDB:DatabaseName"] 
                ?? throw new InvalidOperationException("MongoDB數據庫名稱未配置");

            // 初始化連接狀態
            _connectionStatus = new MongoDBConnectionStatus
            {
                IsConnected = false,
                LastConnectedTime = DateTime.MinValue,
                RetryCount = 0,
                DatabaseName = _databaseName
            };

            _logger.LogInformation("MongoDB連接管理器已初始化，目標資料庫: {DatabaseName}", _databaseName);
        }

        #endregion

        #region Public Methods

        /// <summary> 獲取指定集合的連接 </summary>
        public async Task<IMongoCollection<T>> GetCollectionAsync<T>(string collectionName)
        {
            if (string.IsNullOrWhiteSpace(collectionName))
                throw new ArgumentException("集合名稱不能為空", nameof(collectionName));

            var database = await EnsureConnectionAsync();
            return database.GetCollection<T>(collectionName);
        }

        /// <summary> 執行連接健康檢查 </summary>
        public async Task<bool> HealthCheckAsync()
        {
            try
            {
                // 避免頻繁健康檢查
                if (DateTime.UtcNow - _lastHealthCheck < _healthCheckInterval && _connectionStatus.IsConnected)
                {
                    return true;
                }

                await _connectionSemaphore.WaitAsync(_connectionTimeout);
                try
                {
                    if (_database == null)
                    {
                        await InitializeConnectionAsync();
                    }

                    // 執行簡單的 ping 操作
                    var stopwatch = Stopwatch.StartNew();
                    var result = await _database!.RunCommandAsync<BsonDocument>(new BsonDocument("ping", 1));
                    stopwatch.Stop();

                    _connectionStatus.IsConnected = result["ok"].AsDouble == 1.0;
                    _connectionStatus.LatencyMs = stopwatch.ElapsedMilliseconds;
                    _connectionStatus.LastConnectedTime = DateTime.UtcNow;
                    _connectionStatus.ErrorMessage = null;
                    _lastHealthCheck = DateTime.UtcNow;

                    if (_connectionStatus.IsConnected)
                    {
                        _logger.LogDebug("MongoDB健康檢查成功，延遲: {LatencyMs}ms", _connectionStatus.LatencyMs);
                    }

                    return _connectionStatus.IsConnected;
                }
                finally
                {
                    _connectionSemaphore.Release();
                }
            }
            catch (Exception ex)
            {
                _connectionStatus.IsConnected = false;
                _connectionStatus.ErrorMessage = ex.Message;
                _logger.LogWarning(ex, "MongoDB健康檢查失敗");
                return false;
            }
        }

        /// <summary> 重置連接 </summary>
        public async Task<bool> ResetConnectionAsync()
        {
            try
            {
                await _connectionSemaphore.WaitAsync(_connectionTimeout);
                try
                {
                    _logger.LogInformation("正在重置MongoDB連接...");

                    // 清理現有連接
                    _mongoClient = null;
                    _database = null;
                    _connectionStatus.IsConnected = false;
                    _connectionStatus.RetryCount++;

                    // 重新初始化連接
                    await InitializeConnectionAsync();

                    var isHealthy = await HealthCheckAsync();
                    if (isHealthy)
                    {
                        _logger.LogInformation("MongoDB連接重置成功");
                        return true;
                    }
                    else
                    {
                        _logger.LogError("MongoDB連接重置後健康檢查失敗");
                        return false;
                    }
                }
                finally
                {
                    _connectionSemaphore.Release();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "MongoDB連接重置失敗");
                _connectionStatus.ErrorMessage = ex.Message;
                return false;
            }
        }

        /// <summary> 獲取連接狀態資訊 </summary>
        public async Task<MongoDBConnectionStatus> GetConnectionStatusAsync()
        {
            // 如果連接狀態未知，執行健康檢查
            if (!_connectionStatus.IsConnected && _connectionStatus.LastConnectedTime == DateTime.MinValue)
            {
                await HealthCheckAsync();
            }

            return new MongoDBConnectionStatus
            {
                IsConnected = _connectionStatus.IsConnected,
                LastConnectedTime = _connectionStatus.LastConnectedTime,
                ErrorMessage = _connectionStatus.ErrorMessage,
                RetryCount = _connectionStatus.RetryCount,
                LatencyMs = _connectionStatus.LatencyMs,
                DatabaseName = _connectionStatus.DatabaseName,
                ServerVersion = _connectionStatus.ServerVersion
            };
        }

        /// <summary> 測試連接並執行簡單操作 </summary>
        public async Task<bool> TestConnectionAsync()
        {
            try
            {
                var database = await EnsureConnectionAsync();
                
                // 執行簡單的集合列表操作
                var collections = await database.ListCollectionNamesAsync();
                await collections.ToListAsync();

                _logger.LogDebug("MongoDB連接測試成功");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "MongoDB連接測試失敗");
                return false;
            }
        }

        #endregion

        #region Private Methods

        /// <summary> 確保連接可用 </summary>
        private async Task<IMongoDatabase> EnsureConnectionAsync()
        {
            if (_database != null && _connectionStatus.IsConnected)
            {
                return _database;
            }

            await _connectionSemaphore.WaitAsync(_connectionTimeout);
            try
            {
                if (_database == null || !_connectionStatus.IsConnected)
                {
                    await InitializeConnectionAsync();
                }

                return _database ?? throw new InvalidOperationException("無法建立MongoDB連接");
            }
            finally
            {
                _connectionSemaphore.Release();
            }
        }

        /// <summary> 初始化連接 </summary>
        private async Task InitializeConnectionAsync()
        {
            try
            {
                _logger.LogDebug("正在初始化MongoDB連接...");

                // 配置 MongoDB 客戶端設定
                var settings = MongoClientSettings.FromConnectionString(_connectionString);
                settings.ConnectTimeout = _connectionTimeout;
                settings.ServerSelectionTimeout = _connectionTimeout;
                settings.SocketTimeout = _connectionTimeout;

                // 創建客戶端和資料庫連接
                _mongoClient = new MongoClient(settings);
                _database = _mongoClient.GetDatabase(_databaseName);

                // 執行連接測試
                var result = await _database.RunCommandAsync<BsonDocument>(new BsonDocument("ping", 1));
                
                if (result["ok"].AsDouble == 1.0)
                {
                    _connectionStatus.IsConnected = true;
                    _connectionStatus.LastConnectedTime = DateTime.UtcNow;
                    _connectionStatus.ErrorMessage = null;

                    // 獲取伺服器版本資訊
                    try
                    {
                        var buildInfo = await _database.RunCommandAsync<BsonDocument>(new BsonDocument("buildInfo", 1));
                        _connectionStatus.ServerVersion = buildInfo["version"].AsString;
                    }
                    catch
                    {
                        _connectionStatus.ServerVersion = "Unknown";
                    }

                    _logger.LogInformation("MongoDB連接初始化成功，伺服器版本: {ServerVersion}", 
                        _connectionStatus.ServerVersion);
                }
                else
                {
                    throw new InvalidOperationException("MongoDB ping 命令返回失敗狀態");
                }
            }
            catch (Exception ex)
            {
                _connectionStatus.IsConnected = false;
                _connectionStatus.ErrorMessage = ex.Message;
                _logger.LogError(ex, "MongoDB連接初始化失敗");
                throw;
            }
        }

        #endregion

        #region IDisposable Implementation

        /// <summary> 釋放資源 </summary>
        public void Dispose()
        {
            if (!_disposed)
            {
                _connectionSemaphore?.Dispose();
                _disposed = true;
                _logger.LogDebug("MongoDB連接管理器已釋放資源");
            }
        }

        #endregion
    }
}
