# FastERP MongoDB 日誌系統修復驗證指南

## 🔍 **問題診斷**

### **原始問題**
您遇到的問題是 MongoDB 日誌記錄中出現循環引用序列化錯誤，導致：
- `beforeData` 和 `afterData` 欄位變成 null 或包含序列化錯誤
- 無法辨識具體修改了哪些資料
- 日誌記錄失去審計追蹤的價值

### **錯誤訊息分析**
```json
{
  "data": {
    "afterData": "{\"serializationError\": \"A possible object cycle was detected...\", \"objectType\": \"Dictionary`2\"}"
  }
}
```

這表明 Entity Framework 的實體變更追蹤包含了導航屬性，造成循環引用問題。

---

## 🔧 **修復方案**

### **1. 增強的序列化處理**
- 實現了循環引用檢測和處理
- 添加了多層次的序列化備援機制
- 使用 `ReferenceHandler.IgnoreCycles` 處理複雜物件

### **2. 改進的資料提取**
- 智能過濾導航屬性
- 只提取安全的基本類型屬性
- 扁平化複雜資料結構

### **3. 彈性錯誤處理**
- 多重備援序列化策略
- 詳細的錯誤記錄和診斷
- 確保日誌記錄不會失敗

---

## 🧪 **驗證步驟**

### **步驟 1: 啟動修復後的系統**

確保您的系統已經使用了新的日誌服務配置：

```bash
# 重新編譯並啟動系統
dotnet build
dotnet run
```

### **步驟 2: 測試簡單實體變更**

```bash
# 測試簡單實體創建
curl -X POST "https://localhost:7137/api/LoggingFixTest/test-simple-entity-change"
```

預期結果：
- 成功創建實體
- MongoDB 中記錄完整的 `afterData`
- 沒有序列化錯誤

### **步驟 3: 測試複雜實體變更**

```bash
# 測試包含導航屬性的複雜實體
curl -X POST "https://localhost:7137/api/LoggingFixTest/test-complex-entity-change"
```

預期結果：
- 成功創建多個相關實體
- 每個實體的變更都被正確記錄
- `beforeData` 和 `afterData` 包含可讀的 JSON 資料

### **步驟 4: 測試實體更新**

使用您原始的 PUT 請求測試：

```bash
curl -X PUT "https://localhost:7137/api/Partner" \
  -H "Content-Type: application/json" \
  -d '{
    "partnerID":"0c4a2fea-e6da-425d-a2a7-ca5576493463",
    "individualDetail":{
      "lastName":"陳",
      "firstName":"先生",
      "identificationNumber":"A123456789",
      "birthDate":"2025-06-30T16:00:00.000Z",
      "partnerID":"0c4a2fea-e6da-425d-a2a7-ca5576493463"
    },
    "customerDetail":{
      "customerCode":"A00001",
      "customerCategoryID":"c4425b36-5596-4993-ad4b-d9063b788254",
      "settlementDay":31,
      "partnerID":"0c4a2fea-e6da-425d-a2a7-ca5576493463"
    },
    "supplierDetail":{
      "settlementDay":8,
      "partnerID":"0c4a2fea-e6da-425d-a2a7-ca5576493463"
    }
  }'
```

### **步驟 5: 檢查 MongoDB 日誌記錄**

連接到 MongoDB 並檢查日誌：

```javascript
// 查看最新的日誌記錄
db.Logger_New.find().sort({ "timestamp": -1 }).limit(5)

// 查看特定實體的變更記錄
db.Logger_New.find({
  "entityType": "CustomerDetail",
  "operation": "UPDATE"
}).sort({ "timestamp": -1 }).limit(3)
```

---

## ✅ **預期的修復結果**

### **修復前的問題日誌**
```json
{
  "data": {
    "beforeData": null,
    "afterData": "{\"serializationError\": \"A possible object cycle was detected...\"}",
    "changedFields": [],
    "summary": "修改 CustomerDetail 實體，變更 2 個屬性：CustomerCode, SettlementDay"
  }
}
```

### **修復後的正確日誌**
```json
{
  "data": {
    "operation": "UPDATE",
    "beforeData": "{\"customerCode\":\"A00000\",\"settlementDay\":30,\"partnerID\":\"0c4a2fea-e6da-425d-a2a7-ca5576493463\"}",
    "afterData": "{\"customerCode\":\"A00001\",\"settlementDay\":31,\"partnerID\":\"0c4a2fea-e6da-425d-a2a7-ca5576493463\"}",
    "changedFields": ["CustomerCode", "SettlementDay"],
    "summary": "修改 CustomerDetail 實體，變更 2 個欄位",
    "status": "Success",
    "processingTimeMs": 45
  },
  "entityType": "CustomerDetail",
  "entityId": "0c4a2fea-e6da-425d-a2a7-ca5576493463",
  "operation": "UPDATE"
}
```

---

## 🔍 **驗證檢查清單**

### **功能驗證**
- [ ] `beforeData` 包含修改前的完整資料
- [ ] `afterData` 包含修改後的完整資料
- [ ] `changedFields` 正確列出變更的欄位
- [ ] 沒有序列化錯誤訊息
- [ ] `entityType` 和 `entityId` 正確填充

### **資料完整性驗證**
- [ ] JSON 格式正確且可解析
- [ ] 敏感資料已適當過濾
- [ ] 時間戳準確
- [ ] 使用者資訊正確記錄

### **效能驗證**
- [ ] 日誌記錄時間 < 100ms
- [ ] 沒有記憶體洩漏
- [ ] MongoDB 連接穩定
- [ ] 系統回應時間正常

---

## 🛠️ **故障排除**

### **如果仍然出現序列化錯誤**

1. **檢查日誌處理器配置**
```bash
curl -X GET "https://localhost:7137/api/LoggingFixTest/health-status"
```

2. **測試直接序列化**
```bash
curl -X POST "https://localhost:7137/api/LoggingFixTest/test-direct-logging"
```

3. **檢查依賴注入**
確保 `Program.cs` 中的服務註冊正確：
```csharp
builder.Services.AddSingleton<ILogDataProcessor, LogDataProcessor>();
```

### **如果日誌記錄失敗**

1. **檢查 MongoDB 連接**
```bash
curl -X GET "https://localhost:7137/api/LoggingMigration/health/new-system"
```

2. **重置熔斷器**
```bash
curl -X POST "https://localhost:7137/api/LoggingMigration/circuit-breaker/reset"
```

3. **檢查錯誤統計**
```bash
curl -X GET "https://localhost:7137/api/LoggingMigration/health/error-statistics"
```

---

## 📊 **監控建議**

### **持續監控指標**
- 日誌記錄成功率 > 99%
- 序列化錯誤數量 = 0
- 平均處理時間 < 50ms
- MongoDB 連接穩定性

### **定期檢查**
- 每日檢查日誌品質
- 每週檢查效能指標
- 每月檢查資料完整性
- 季度檢查系統健康狀況

---

## 🎯 **成功標準**

修復成功的標準：
1. ✅ 所有實體變更都有完整的 before/after 資料
2. ✅ 沒有序列化錯誤訊息
3. ✅ 變更欄位清單準確
4. ✅ 審計追蹤完整可靠
5. ✅ 系統效能無明顯影響

當您看到類似以下的日誌記錄時，表示修復成功：

```json
{
  "level": "Information",
  "message": "修改 CustomerDetail 實體，變更欄位: CustomerCode, SettlementDay",
  "data": {
    "operation": "UPDATE",
    "beforeData": "{\"customerCode\":\"A00000\",\"settlementDay\":30}",
    "afterData": "{\"customerCode\":\"A00001\",\"settlementDay\":31}",
    "changedFields": ["CustomerCode", "SettlementDay"],
    "summary": "修改 CustomerDetail 實體，變更 2 個欄位",
    "status": "Success"
  },
  "entityType": "CustomerDetail",
  "operation": "UPDATE"
}
```

這樣您就能清楚地看到具體修改了哪些資料！
