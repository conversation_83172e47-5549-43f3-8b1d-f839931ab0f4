﻿using Microsoft.EntityFrameworkCore;
using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Server.Tools;
using FAST_ERP_Backend.Interfaces.Common;
using FAST_ERP_Backend.Interfaces.Pms;
using FAST_ERP_Backend.Interfaces.Ims;
using FAST_ERP_Backend.Services.Common;
using FAST_ERP_Backend.Services.Pms;
using FAST_ERP_Backend.Services.Ims;
// using log4net.Config;
// using log4net;
using System.Reflection;
using Microsoft.OpenApi.Models;
using FAST_ERP_Backend.Middlewares;
using Microsoft.IdentityModel.Tokens;
using Microsoft.Extensions.FileProviders;
using Microsoft.AspNetCore.Mvc;
using FAST_ERP_Backend.Interfaces.Pas;
using FAST_ERP_Backend.Services.Pas;
using AutoMapper;

var builder = WebApplication.CreateBuilder(args);

#region Logging Configuration
// 配置系統日誌
builder.Logging.ClearProviders();
builder.Logging.AddConsole();
builder.Logging.AddDebug();

// 設置日誌級別
if (builder.Environment.IsDevelopment())
{
    builder.Logging.SetMinimumLevel(LogLevel.Debug);
}
else
{
    builder.Logging.SetMinimumLevel(LogLevel.Information);
}

// 註冊新版彈性MongoDB日誌服務
builder.Services.AddSingleton<IMongoDBConnectionManager, MongoDBConnectionManager>();
builder.Services.AddSingleton<LogProcessingOptions>();
builder.Services.AddSingleton<ILogDataProcessor, LogDataProcessor>();
builder.Services.AddSingleton<IResilientLoggerService, ResilientMongoDBLoggerService>();

// 主要日誌服務（使用新版彈性服務）
builder.Services.AddSingleton<ILoggerService, ResilientMongoDBLoggerService>();

// 保留舊版服務供遷移期間使用（可選）
// builder.Services.AddSingleton<MongoDBLoggerService>();
// builder.Services.AddSingleton<ILoggerService, DualWriteLoggerService>();
#endregion

#region Core Services
// 註冊HttpContextAccessor
builder.Services.AddHttpContextAccessor();

// 註冊 AutoMapper
builder.Services.AddAutoMapper(typeof(Program).Assembly);
#endregion

// 註冊參數值處理服務
builder.Services.AddSingleton<IParameterValueService, ParameterValueService>();

// 註冊DbContext綁定DefaultConnection
builder.Services.AddDbContext<ERPDbContext>((serviceProvider, options) =>
{
    options.UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnection"));
    // ERPDbContext 會通過建構函式自動獲得 ILoggerService 和 ILogDataProcessor
});

// 註冊 IConfiguration，讓 DI 可以提供配置
builder.Services.AddSingleton<IConfiguration>(builder.Configuration);

// 共用function.
builder.Services.AddTransient<Baseform>();
builder.Services.AddTransient<EmployeeClass>();
builder.Services.AddTransient<EncryptionHelper>();
// 註冊 TokenHandler 服務
builder.Services.AddScoped<TokenHandler>();

// 共用token解析服務
builder.Services.AddScoped<ICurrentUserService, CurrentUserService>();

// SignalR.
builder.Services.AddTransient<ISignalRMessageService, SignalRMessageService>();


#region Services
// Common
builder.Services.AddTransient<IUsersService, UsersService>();
builder.Services.AddTransient<IDepartmentService, DepartmentService>();
builder.Services.AddTransient<IEnterpriseGroupsService, EnterpriseGroupsService>();
builder.Services.AddTransient<IRolesService, RolesService>();
builder.Services.AddTransient<IRolesPermissionsService, RolesPermissionsService>();
builder.Services.AddTransient<IAuditLogsService, AuditLogsService>();
builder.Services.AddTransient<ISystemGroupsService, SystemGroupsService>();
builder.Services.AddTransient<ISystemMenuService, SystemMenuService>();
builder.Services.AddTransient<ISystemParametersService, SystemParametersService>();
builder.Services.AddTransient<ISystemParametersItemService, SystemParametersItemService>();
builder.Services.AddTransient<IPositionService, PositionService>();
builder.Services.AddTransient<IDivisionService, DivisionService>();
builder.Services.AddTransient<IUnitService, UnitService>();
builder.Services.AddTransient<ICityService, CityService>();
builder.Services.AddTransient<IDistrictService, DistrictService>();
builder.Services.AddTransient<IEnterpriseImageService, EnterpriseImageService>();
builder.Services.AddTransient<IFileListService, FileListService>();
builder.Services.AddTransient<IFileUploadService, FileUploadService>();

// PMS
builder.Services.AddTransient<IAssetService, AssetService>();
builder.Services.AddTransient<IInsuranceUnitService, InsuranceUnitService>();
builder.Services.AddTransient<IManufacturerService, ManufacturerService>();
builder.Services.AddTransient<IStorageLocationService, StorageLocationService>();
builder.Services.AddTransient<IDepreciationFormService, DepreciationFormService>();
builder.Services.AddTransient<IDepreciationFormDetailService, DepreciationFormDetailService>();
builder.Services.AddTransient<IAssetAccountService, AssetAccountService>();
builder.Services.AddTransient<IAssetSubAccountService, AssetSubAccountService>();
builder.Services.AddTransient<IAssetSourceService, AssetSourceService>();
builder.Services.AddTransient<IAssetCategoryService, AssetCategoryService>();
builder.Services.AddTransient<IAccessoryEquipmentService, AccessoryEquipmentService>();
builder.Services.AddTransient<IAmortizationSourceService, AmortizationSourceService>();
builder.Services.AddTransient<IAssetStatusService, AssetStatusService>();
builder.Services.AddTransient<IEquipmentTypeService, EquipmentTypeService>();
builder.Services.AddTransient<IPmsUserRoleService, PmsUserRoleService>();
builder.Services.AddTransient<IPmsSystemParameterService, PmsSystemParameterService>();
builder.Services.AddTransient<IAssetCarryOutService, AssetCarryOutService>();
builder.Services.AddTransient<IVendorMaintenanceService, VendorMaintenanceService>();
builder.Services.AddTransient<IAssetLocationTransferService, AssetLocationTransferService>();

// PAS
builder.Services.AddTransient<IEmployeeService, EmployeeService>();
builder.Services.AddTransient<IEducationService, EducationService>();
builder.Services.AddTransient<ITrainService, TrainService>();
builder.Services.AddTransient<IExaminationService, ExaminationService>();
builder.Services.AddTransient<ICertificationService, CertificationService>();
builder.Services.AddTransient<IUndergoService, UndergoService>();
builder.Services.AddTransient<IEnsureService, EnsureService>();
builder.Services.AddTransient<ISuspendService, SuspendService>();
builder.Services.AddTransient<ISalaryService, SalaryService>();
builder.Services.AddTransient<IHensureService, HensureService>();
builder.Services.AddTransient<IDependentService, DependentService>();
builder.Services.AddTransient<IPerformancePointGroupService, PerformancePointGroupService>();
builder.Services.AddTransient<IPerformancePointTypeService, PerformancePointTypeService>();
builder.Services.AddTransient<IPerformancePointRecordService, PerformancePointRecordService>();
builder.Services.AddTransient<IRegularSalaryItemService, RegularSalaryItemService>();
builder.Services.AddTransient<IEmployeeRegularSalaryService, EmployeeRegularSalaryService>();
builder.Services.AddTransient<ISalaryPointService, SalaryPointService>();
builder.Services.AddTransient<IInsuranceGradeService, InsuranceGradeService>();
builder.Services.AddTransient<IInsuranceHistoryService, InsuranceHistoryService>();
builder.Services.AddTransient<IPromotionService, PromotionService>();
builder.Services.AddTransient<IServiceDepartmentChangeService, ServiceDepartmentChangeService>();
builder.Services.AddTransient<IExpenseDepartmentChangeService, ExpenseDepartmentChangeService>();


builder.Services.AddTransient<IPasOptionParameterService, PasOptionParameterService>();

// IMS
builder.Services.AddTransient<IPartnerService, PartnerService>();
builder.Services.AddTransient<IPriceTypeService, PriceTypeService>();
builder.Services.AddTransient<IItemService, ItemService>();
builder.Services.AddTransient<IItemPriceService, ItemPriceService>();
builder.Services.AddTransient<IItemCategoryService, ItemCategoryService>();
builder.Services.AddTransient<ICustomerCategoryService, CustomerCategoryService>();
builder.Services.AddTransient<ISupplierCategoryService, SupplierCategoryService>();
builder.Services.AddTransient<TestDataService>(); // 測試資料產生服務
#endregion

//註冊Swagger註解文件
builder.Services.AddSwaggerGen(options =>
{
    // 設定分組邏輯
    options.DocInclusionPredicate((docName, apiDesc) =>
    {
        var actionDescriptor = apiDesc.ActionDescriptor as Microsoft.AspNetCore.Mvc.Controllers.ControllerActionDescriptor;

        if (actionDescriptor == null)
            return false;

        // 取得控制器的命名空間
        var namespaceName = actionDescriptor.ControllerTypeInfo.Namespace;

        // 比對命名空間是否符合分組名稱
        return namespaceName != null && namespaceName.EndsWith(docName, StringComparison.OrdinalIgnoreCase);
    });

    // 動態為每個命名空間創建 Swagger 文檔
    var apiNamespaces = AppDomain.CurrentDomain.GetAssemblies()
        .SelectMany(a => a.GetTypes())
        .Where(t => t.IsClass && t.IsSubclassOf(typeof(ControllerBase)) && t.Namespace != null && !t.Namespace.StartsWith("Microsoft.AspNetCore.Mvc"))
        .Select(t => t.Namespace)
        .Distinct()
        .ToList();

    foreach (var ns in apiNamespaces)
    {
        options.SwaggerDoc(ns, new Microsoft.OpenApi.Models.OpenApiInfo
        {
            Title = $"{ns} API",
            Version = "v1"
        });
    }

    // 啟用註解功能
    options.EnableAnnotations();
    //Swagger JWT Token 驗證功能
    options.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
    {
        Name = "Authorization",
        Type = SecuritySchemeType.Http,
        Scheme = "bearer",
        BearerFormat = "JWT",
        In = ParameterLocation.Header,
        Description = "輸入 JWT Token"
    });

    options.AddSecurityRequirement(new OpenApiSecurityRequirement
    {
        {
            new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference
                {
                    Type = ReferenceType.SecurityScheme,
                    Id = "Bearer"
                }
            },
            new string[] {}
        }
    });
});

// 設定 Swagger 讀取 XML 檔案
var xmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
builder.Services.AddSwaggerGen(options =>
{
    options.IncludeXmlComments(xmlPath);
});

// 其他服務註冊.
builder.Services.AddHttpClient(); // 註冊 HttpClient (呼叫外部用api)
builder.Services.AddSignalR(); // SignalR

builder.Services.AddControllers();
builder.Services.AddCors(options =>
{
    //僅開啟https://localhost:3000的要求
    options.AddPolicy("AllowMyOrigin",
       policy =>
       {
           policy.WithOrigins("https://localhost:3000", "http://localhost:3000") // 允許來自這個來源的請求
                 .AllowAnyHeader()
                 .AllowAnyMethod()
                 .AllowCredentials();
       });
});

// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

var app = builder.Build();

// Configure the HTTP request pipeline.
// 1. 全域例外處理 (最先執行)
app.UseGlobalExceptionHandler();

// 開發環境專用設定
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        //啟用 Swagger 中介軟體
        var apiNamespaces = AppDomain.CurrentDomain.GetAssemblies()
            .SelectMany(a => a.GetTypes())
            .Where(t => t.IsClass && t.IsSubclassOf(typeof(ControllerBase)) && t.Namespace != null && !t.Namespace.StartsWith("Microsoft.AspNetCore.Mvc"))
            .Select(t => t.Namespace)
            .Distinct()
            .OrderBy(ns => ns)  // 這裡改為按字母順序升序排列
            .ToList();

        foreach (var ns in apiNamespaces)
        {
            c.SwaggerEndpoint($"/swagger/{ns}/swagger.json", $"{ns} API v1");
        }
    });
}
else
{
    app.UseExceptionHandler("/Error");
    app.UseHsts();
}

// 2. HTTPS 重定向 (在靜態檔案之前)
app.UseHttpsRedirection();

// 3. 靜態檔案處理
// 確保 wwwroot 和 uploads 目錄存在
var wwwrootPath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot");
if (!Directory.Exists(wwwrootPath))
{
    Directory.CreateDirectory(wwwrootPath);
}

var uploadsPath = Path.Combine(wwwrootPath, "uploads");
if (!Directory.Exists(uploadsPath))
{
    Directory.CreateDirectory(uploadsPath);
}

// 圖片存取權限
app.UseStaticFiles(new StaticFileOptions
{
    FileProvider = new PhysicalFileProvider(uploadsPath),
    RequestPath = "/uploads"
});

// 4. 路由
app.UseRouting();

// 5. CORS (在路由之後，驗證之前)
app.UseCors("AllowMyOrigin");

// 6. 驗證和授權 (注意：需要先加入 Authentication 服務)
// app.UseAuthentication(); // 如果有 JWT 驗證，請取消註解
app.UseAuthorization();

// 7. 自定義中介軟體
app.UsePasswordValidation();
//群組驗證
//app.UseGroupPermissions();
//驗證選單權限
//app.UseMenuPermissions();

// 8. 端點映射
app.MapHub<SignalRHub>("/hubs/SignalRHub"); // SignalRHub導向位置
app.MapControllers();

app.Run();