using System;
using System.Threading.Tasks;
using FAST_ERP_Backend.Models.Common.Logging;

namespace FAST_ERP_Backend.Interfaces.Common
{
    /// <summary>
    /// 具有彈性和錯誤處理能力的日誌服務介面
    /// 提供重試機制、熔斷器模式和降級處理
    /// </summary>
    public interface IResilientLoggerService : ILoggerService
    {
        /// <summary> 嘗試記錄日誌（帶重試機制） </summary>
        /// <param name="entry">日誌記錄</param>
        /// <param name="maxRetries">最大重試次數</param>
        /// <returns>記錄是否成功</returns>
        Task<bool> TryLogAsync(SimpleLogEntry entry, int maxRetries = 3);

        /// <summary> 批次記錄日誌 </summary>
        /// <param name="entries">日誌記錄列表</param>
        /// <param name="maxRetries">最大重試次數</param>
        /// <returns>成功記錄的數量</returns>
        Task<int> TryLogBatchAsync(SimpleLogEntry[] entries, int maxRetries = 3);

        /// <summary> 獲取日誌服務健康狀態 </summary>
        /// <returns>健康狀態資訊</returns>
        Task<LoggingHealthStatus> GetHealthStatusAsync();

        /// <summary> 啟用或停用熔斷器 </summary>
        /// <param name="enabled">是否啟用</param>
        void EnableCircuitBreaker(bool enabled);

        /// <summary> 重置熔斷器狀態 </summary>
        Task ResetCircuitBreakerAsync();

        /// <summary> 獲取錯誤統計資訊 </summary>
        /// <returns>錯誤統計</returns>
        Task<LoggingErrorStatistics> GetErrorStatisticsAsync();

        /// <summary> 清除錯誤統計 </summary>
        Task ClearErrorStatisticsAsync();

        /// <summary> 設定降級模式 </summary>
        /// <param name="enabled">是否啟用降級模式</param>
        void SetFallbackMode(bool enabled);
    }

    /// <summary>
    /// 日誌服務健康狀態
    /// </summary>
    public class LoggingHealthStatus
    {
        /// <summary> 服務是否健康 </summary>
        public bool IsHealthy { get; set; }

        /// <summary> MongoDB 連接狀態 </summary>
        public bool IsMongoDBConnected { get; set; }

        /// <summary> 熔斷器狀態 </summary>
        public CircuitBreakerState CircuitBreakerState { get; set; }

        /// <summary> 是否處於降級模式 </summary>
        public bool IsFallbackMode { get; set; }

        /// <summary> 最後成功記錄時間 </summary>
        public DateTime LastSuccessfulLog { get; set; }

        /// <summary> 最後錯誤時間 </summary>
        public DateTime LastErrorTime { get; set; }

        /// <summary> 最後錯誤訊息 </summary>
        public string? LastErrorMessage { get; set; }

        /// <summary> 平均回應時間（毫秒） </summary>
        public double AverageResponseTimeMs { get; set; }

        /// <summary> 成功率（百分比） </summary>
        public double SuccessRate { get; set; }

        /// <summary> 待處理的日誌數量 </summary>
        public int PendingLogCount { get; set; }

        /// <summary> 服務啟動時間 </summary>
        public DateTime ServiceStartTime { get; set; }

        /// <summary> 總處理日誌數量 </summary>
        public long TotalProcessedLogs { get; set; }
    }

    /// <summary>
    /// 熔斷器狀態
    /// </summary>
    public enum CircuitBreakerState
    {
        /// <summary> 關閉（正常運作） </summary>
        Closed,
        /// <summary> 開啟（停止呼叫） </summary>
        Open,
        /// <summary> 半開啟（測試恢復） </summary>
        HalfOpen,
        /// <summary> 已停用 </summary>
        Disabled
    }

    /// <summary>
    /// 日誌錯誤統計
    /// </summary>
    public class LoggingErrorStatistics
    {
        /// <summary> 總錯誤數量 </summary>
        public long TotalErrors { get; set; }

        /// <summary> MongoDB 連接錯誤數量 </summary>
        public long MongoDBConnectionErrors { get; set; }

        /// <summary> 序列化錯誤數量 </summary>
        public long SerializationErrors { get; set; }

        /// <summary> 超時錯誤數量 </summary>
        public long TimeoutErrors { get; set; }

        /// <summary> 其他錯誤數量 </summary>
        public long OtherErrors { get; set; }

        /// <summary> 最近 24 小時錯誤數量 </summary>
        public long ErrorsLast24Hours { get; set; }

        /// <summary> 最近 1 小時錯誤數量 </summary>
        public long ErrorsLastHour { get; set; }

        /// <summary> 連續錯誤數量 </summary>
        public int ConsecutiveErrors { get; set; }

        /// <summary> 最後重置時間 </summary>
        public DateTime LastResetTime { get; set; }

        /// <summary> 錯誤率（百分比） </summary>
        public double ErrorRate { get; set; }

        /// <summary> 常見錯誤類型統計 </summary>
        public Dictionary<string, int> ErrorTypeStatistics { get; set; } = new Dictionary<string, int>();
    }

    /// <summary>
    /// 重試策略選項
    /// </summary>
    public class RetryOptions
    {
        /// <summary> 最大重試次數 </summary>
        public int MaxRetries { get; set; } = 3;

        /// <summary> 基礎延遲時間（毫秒） </summary>
        public int BaseDelayMs { get; set; } = 1000;

        /// <summary> 最大延遲時間（毫秒） </summary>
        public int MaxDelayMs { get; set; } = 30000;

        /// <summary> 延遲倍數 </summary>
        public double DelayMultiplier { get; set; } = 2.0;

        /// <summary> 是否使用指數退避 </summary>
        public bool UseExponentialBackoff { get; set; } = true;

        /// <summary> 是否添加隨機抖動 </summary>
        public bool UseJitter { get; set; } = true;
    }

    /// <summary>
    /// 熔斷器選項
    /// </summary>
    public class CircuitBreakerOptions
    {
        /// <summary> 失敗閾值 </summary>
        public int FailureThreshold { get; set; } = 5;

        /// <summary> 成功閾值（半開啟狀態） </summary>
        public int SuccessThreshold { get; set; } = 3;

        /// <summary> 超時時間（毫秒） </summary>
        public int TimeoutMs { get; set; } = 60000;

        /// <summary> 監控時間窗口（毫秒） </summary>
        public int MonitoringWindowMs { get; set; } = 300000; // 5 分鐘

        /// <summary> 是否啟用 </summary>
        public bool Enabled { get; set; } = true;
    }
}
