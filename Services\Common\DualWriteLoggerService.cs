using System;
using System.Threading.Tasks;
using FAST_ERP_Backend.Interfaces.Common;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace FAST_ERP_Backend.Services.Common
{
    /// <summary>
    /// 雙寫日誌服務
    /// 在遷移期間同時寫入新舊日誌系統，確保資料一致性和平穩過渡
    /// </summary>
    public class DualWriteLoggerService : ILoggerService
    {
        #region Private Fields

        private readonly ILoggerService _legacyService;
        private readonly IResilientLoggerService _newService;
        private readonly ILogger<DualWriteLoggerService> _logger;
        private readonly IConfiguration _configuration;

        // 配置選項
        private readonly bool _enableNewService;
        private readonly bool _enableLegacyService;
        private readonly bool _failOnLegacyError;
        private readonly bool _failOnNewError;

        #endregion

        #region Constructor

        /// <summary>
        /// 初始化雙寫日誌服務
        /// </summary>
        /// <param name="legacyService">舊日誌服務</param>
        /// <param name="newService">新日誌服務</param>
        /// <param name="logger">系統日誌</param>
        /// <param name="configuration">配置服務</param>
        public DualWriteLoggerService(
            ILoggerService legacyService,
            IResilientLoggerService newService,
            ILogger<DualWriteLoggerService> logger,
            IConfiguration configuration)
        {
            _legacyService = legacyService ?? throw new ArgumentNullException(nameof(legacyService));
            _newService = newService ?? throw new ArgumentNullException(nameof(newService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));

            // 讀取配置
            _enableNewService = _configuration.GetValue<bool>("Logging:Migration:EnableNewService", true);
            _enableLegacyService = _configuration.GetValue<bool>("Logging:Migration:EnableLegacyService", true);
            _failOnLegacyError = _configuration.GetValue<bool>("Logging:Migration:FailOnLegacyError", false);
            _failOnNewError = _configuration.GetValue<bool>("Logging:Migration:FailOnNewError", false);

            _logger.LogInformation("雙寫日誌服務已初始化 - 新系統: {NewEnabled}, 舊系統: {LegacyEnabled}", 
                _enableNewService, _enableLegacyService);
        }

        #endregion

        #region ILoggerService Implementation

        /// <summary> 記錄調試日誌 </summary>
        public async Task LogDebugAsync(string message, string source = "System")
        {
            await DualWriteAsync(
                () => _legacyService.LogDebugAsync(message, source),
                () => _newService.LogDebugAsync(message, source),
                "LogDebugAsync"
            );
        }

        /// <summary> 記錄信息日誌 </summary>
        public async Task LogInfoAsync(string message, string source = "System")
        {
            await DualWriteAsync(
                () => _legacyService.LogInfoAsync(message, source),
                () => _newService.LogInfoAsync(message, source),
                "LogInfoAsync"
            );
        }

        /// <summary> 記錄警告日誌 </summary>
        public async Task LogWarningAsync(string message, string source = "System")
        {
            await DualWriteAsync(
                () => _legacyService.LogWarningAsync(message, source),
                () => _newService.LogWarningAsync(message, source),
                "LogWarningAsync"
            );
        }

        /// <summary> 記錄錯誤日誌 </summary>
        public async Task LogErrorAsync(string message, Exception? exception = null, string source = "System")
        {
            await DualWriteAsync(
                () => _legacyService.LogErrorAsync(message, exception, source),
                () => _newService.LogErrorAsync(message, exception, source),
                "LogErrorAsync"
            );
        }

        /// <summary> 記錄異動日誌 </summary>
        public async Task LogDataAsync<T>(string message, T changedData, string transactionId, string source = "System") where T : class
        {
            await DualWriteAsync(
                () => _legacyService.LogDataAsync(message, changedData, transactionId, source),
                () => _newService.LogDataAsync(message, changedData, transactionId, source),
                "LogDataAsync"
            );
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// 執行雙寫操作
        /// </summary>
        /// <param name="legacyAction">舊系統操作</param>
        /// <param name="newAction">新系統操作</param>
        /// <param name="operationName">操作名稱</param>
        private async Task DualWriteAsync(Func<Task> legacyAction, Func<Task> newAction, string operationName)
        {
            var legacySuccess = false;
            var newSuccess = false;
            Exception? legacyException = null;
            Exception? newException = null;

            // 執行舊系統寫入
            if (_enableLegacyService)
            {
                try
                {
                    await legacyAction();
                    legacySuccess = true;
                    _logger.LogDebug("舊系統日誌寫入成功: {Operation}", operationName);
                }
                catch (Exception ex)
                {
                    legacyException = ex;
                    _logger.LogWarning(ex, "舊系統日誌寫入失敗: {Operation}", operationName);
                }
            }
            else
            {
                legacySuccess = true; // 如果停用，視為成功
            }

            // 執行新系統寫入
            if (_enableNewService)
            {
                try
                {
                    await newAction();
                    newSuccess = true;
                    _logger.LogDebug("新系統日誌寫入成功: {Operation}", operationName);
                }
                catch (Exception ex)
                {
                    newException = ex;
                    _logger.LogWarning(ex, "新系統日誌寫入失敗: {Operation}", operationName);
                }
            }
            else
            {
                newSuccess = true; // 如果停用，視為成功
            }

            // 記錄雙寫結果統計
            await LogDualWriteResult(operationName, legacySuccess, newSuccess, legacyException, newException);

            // 根據配置決定是否拋出例外
            if (_failOnLegacyError && !legacySuccess && legacyException != null)
            {
                throw new InvalidOperationException($"舊系統日誌寫入失敗: {legacyException.Message}", legacyException);
            }

            if (_failOnNewError && !newSuccess && newException != null)
            {
                throw new InvalidOperationException($"新系統日誌寫入失敗: {newException.Message}", newException);
            }

            // 如果兩個系統都失敗，拋出綜合例外
            if (!legacySuccess && !newSuccess)
            {
                var combinedException = new AggregateException(
                    "雙寫日誌系統都失敗",
                    new[] { legacyException, newException }.Where(ex => ex != null)!
                );
                
                _logger.LogError(combinedException, "雙寫日誌系統都失敗: {Operation}", operationName);
                throw combinedException;
            }
        }

        /// <summary>
        /// 記錄雙寫結果統計
        /// </summary>
        private async Task LogDualWriteResult(string operation, bool legacySuccess, bool newSuccess, 
            Exception? legacyException, Exception? newException)
        {
            try
            {
                var result = new
                {
                    Operation = operation,
                    Timestamp = DateTime.UtcNow,
                    LegacySystem = new
                    {
                        Success = legacySuccess,
                        Enabled = _enableLegacyService,
                        Error = legacyException?.Message
                    },
                    NewSystem = new
                    {
                        Success = newSuccess,
                        Enabled = _enableNewService,
                        Error = newException?.Message
                    },
                    OverallResult = (legacySuccess || !_enableLegacyService) && (newSuccess || !_enableNewService) ? "Success" : "Partial"
                };

                // 使用系統日誌記錄統計（避免遞歸）
                _logger.LogDebug("雙寫結果: {@Result}", result);

                // 如果有部分失敗，記錄警告
                if (result.OverallResult == "Partial")
                {
                    _logger.LogWarning("雙寫部分失敗 - 操作: {Operation}, 舊系統: {LegacySuccess}, 新系統: {NewSuccess}", 
                        operation, legacySuccess, newSuccess);
                }
            }
            catch (Exception ex)
            {
                // 統計記錄失敗不應影響主要流程
                _logger.LogError(ex, "記錄雙寫統計時發生錯誤");
            }

            await Task.CompletedTask;
        }

        #endregion

        #region Public Utility Methods

        /// <summary>
        /// 獲取雙寫狀態資訊
        /// </summary>
        /// <returns>雙寫狀態</returns>
        public async Task<object> GetDualWriteStatusAsync()
        {
            try
            {
                var newSystemHealth = await _newService.GetHealthStatusAsync();
                
                return new
                {
                    Configuration = new
                    {
                        NewServiceEnabled = _enableNewService,
                        LegacyServiceEnabled = _enableLegacyService,
                        FailOnLegacyError = _failOnLegacyError,
                        FailOnNewError = _failOnNewError
                    },
                    NewSystemHealth = newSystemHealth,
                    Status = new
                    {
                        IsOperational = _enableNewService || _enableLegacyService,
                        Mode = GetCurrentMode(),
                        Timestamp = DateTime.UtcNow
                    }
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "獲取雙寫狀態時發生錯誤");
                throw;
            }
        }

        /// <summary>
        /// 切換到僅新系統模式
        /// </summary>
        public void SwitchToNewSystemOnly()
        {
            // 這裡應該更新配置或使用配置管理服務
            _logger.LogInformation("請求切換到僅新系統模式 - 需要更新配置並重啟服務");
        }

        /// <summary>
        /// 切換到僅舊系統模式（回滾）
        /// </summary>
        public void SwitchToLegacySystemOnly()
        {
            // 這裡應該更新配置或使用配置管理服務
            _logger.LogInformation("請求切換到僅舊系統模式（回滾） - 需要更新配置並重啟服務");
        }

        /// <summary>
        /// 獲取當前運行模式
        /// </summary>
        private string GetCurrentMode()
        {
            if (_enableNewService && _enableLegacyService)
                return "DualWrite";
            else if (_enableNewService && !_enableLegacyService)
                return "NewSystemOnly";
            else if (!_enableNewService && _enableLegacyService)
                return "LegacySystemOnly";
            else
                return "Disabled";
        }

        #endregion
    }
}
