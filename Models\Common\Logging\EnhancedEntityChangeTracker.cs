using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Reflection;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;

namespace FAST_ERP_Backend.Models.Common.Logging
{
    /// <summary>
    /// 增強型實體變更追蹤器
    /// </summary>
    public class EnhancedEntityChangeTracker
    {
        #region Private Fields
        
        /// <summary> 實體元數據快取 </summary>
        private static readonly ConcurrentDictionary<Type, EntityMetadata> _metadataCache = new();
        
        /// <summary> 安全類型集合 </summary>
        private static readonly HashSet<Type> _safeTypes = new()
        {
            typeof(string), typeof(int), typeof(long), typeof(decimal),
            typeof(bool), typeof(DateTime), typeof(DateTimeOffset),
            typeof(Guid), typeof(byte[]), typeof(float), typeof(double),
            typeof(short), typeof(byte), typeof(char),
            // 可空類型
            typeof(int?), typeof(long?), typeof(decimal?),
            typeof(bool?), typeof(DateTime?), typeof(DateTimeOffset?),
            typeof(Guid?), typeof(float?), typeof(double?),
            typeof(short?), typeof(byte?), typeof(char?)
        };
        
        #endregion
        
        #region Public Methods
        
        /// <summary> 捕獲實體變更快照 </summary>
        /// <param name="changeTracker">變更追蹤器</param>
        /// <returns>實體變更快照</returns>
        public EntityChangeSnapshot CaptureChanges(ChangeTracker changeTracker)
        {
            var snapshot = new EntityChangeSnapshot();
            
            try
            {
                var entries = changeTracker.Entries()
                    .Where(e => e.State != EntityState.Unchanged)
                    .ToList();
                
                foreach (var entry in entries)
                {
                    try
                    {
                        var changeRecord = CreateChangeRecord(entry);
                        if (changeRecord != null)
                        {
                            snapshot.Changes.Add(changeRecord);
                        }
                    }
                    catch (Exception ex)
                    {
                        // 單一實體錯誤不影響整體處理
                        Console.WriteLine($"[EnhancedEntityChangeTracker] 處理實體 {entry.Entity.GetType().Name} 時發生錯誤: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[EnhancedEntityChangeTracker] 捕獲變更快照時發生錯誤: {ex.Message}");
            }
            
            return snapshot;
        }
        
        /// <summary> 創建單一實體變更記錄 </summary>
        /// <param name="entry">實體項目</param>
        /// <returns>實體變更記錄</returns>
        public EntityChangeRecord? CreateChangeRecord(EntityEntry entry)
        {
            try
            {
                var entityType = entry.Entity.GetType();
                var metadata = GetEntityMetadata(entityType);
                
                // 提取實體ID
                var entityId = ExtractEntityId(entry.Entity, metadata);
                if (string.IsNullOrEmpty(entityId))
                {
                    return null; // 無法提取ID的實體跳過
                }
                
                // 確定操作類型
                var operation = entry.State switch
                {
                    EntityState.Added => EntityChangeOperation.Create,
                    EntityState.Modified => EntityChangeOperation.Update,
                    EntityState.Deleted => EntityChangeOperation.Delete,
                    _ => EntityChangeOperation.Update
                };
                
                var changeRecord = new EntityChangeRecord(entityType.Name, entityId, operation);
                
                // 提取使用者ID
                changeRecord.UserId = ExtractUserId(entry.Entity, metadata);
                
                // 根據操作類型提取屬性值
                switch (operation)
                {
                    case EntityChangeOperation.Create:
                        changeRecord.AfterValues = ExtractSafeProperties(entry.Entity, EntityState.Added);
                        break;
                        
                    case EntityChangeOperation.Update:
                        changeRecord.BeforeValues = ExtractSafeProperties(entry.OriginalValues.ToObject(), EntityState.Modified);
                        changeRecord.AfterValues = ExtractSafeProperties(entry.Entity, EntityState.Modified);
                        changeRecord.ChangedProperties = CalculateChangedProperties(changeRecord.BeforeValues, changeRecord.AfterValues);
                        break;
                        
                    case EntityChangeOperation.Delete:
                        changeRecord.BeforeValues = ExtractSafeProperties(entry.Entity, EntityState.Deleted);
                        break;
                }
                
                return changeRecord;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[EnhancedEntityChangeTracker] 創建變更記錄時發生錯誤: {ex.Message}");
                return null;
            }
        }
        
        /// <summary> 提取安全屬性值 </summary>
        /// <param name="entity">實體對象</param>
        /// <param name="state">實體狀態</param>
        /// <returns>安全屬性字典</returns>
        public Dictionary<string, object> ExtractSafeProperties(object entity, EntityState state)
        {
            var result = new Dictionary<string, object>();
            
            if (entity == null) return result;
            
            try
            {
                var entityType = entity.GetType();
                var metadata = GetEntityMetadata(entityType);
                
                foreach (var property in metadata.SafeProperties)
                {
                    try
                    {
                        var value = property.GetValue(entity);
                        if (value != null)
                        {
                            result[property.Name] = ConvertToSafeValue(value);
                        }
                    }
                    catch (Exception ex)
                    {
                        result[property.Name] = $"[提取錯誤: {ex.Message}]";
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[EnhancedEntityChangeTracker] 提取安全屬性時發生錯誤: {ex.Message}");
            }
            
            return result;
        }
        
        #endregion
        
        #region Private Methods
        
        /// <summary> 獲取實體元數據 </summary>
        /// <param name="entityType">實體類型</param>
        /// <returns>實體元數據</returns>
        private EntityMetadata GetEntityMetadata(Type entityType)
        {
            return _metadataCache.GetOrAdd(entityType, type =>
            {
                var metadata = new EntityMetadata(type);
                var properties = type.GetProperties(BindingFlags.Public | BindingFlags.Instance);
                
                foreach (var property in properties)
                {
                    if (IsSafeType(property.PropertyType))
                    {
                        metadata.SafeProperties.Add(property);
                        
                        // 檢查是否為主鍵
                        if (property.GetCustomAttribute<KeyAttribute>() != null || 
                            property.Name.EndsWith("ID", StringComparison.OrdinalIgnoreCase))
                        {
                            metadata.PrimaryKeyProperty = property;
                        }
                        
                        // 檢查是否為使用者ID
                        if (property.Name.Contains("UserId", StringComparison.OrdinalIgnoreCase))
                        {
                            metadata.UserIdProperty = property;
                        }
                    }
                    else if (IsNavigationProperty(property, type))
                    {
                        metadata.NavigationProperties.Add(property);
                    }
                }
                
                return metadata;
            });
        }
        
        /// <summary> 檢查是否為安全類型 </summary>
        /// <param name="type">類型</param>
        /// <returns>是否為安全類型</returns>
        private bool IsSafeType(Type type)
        {
            // 處理可空類型
            var underlyingType = Nullable.GetUnderlyingType(type) ?? type;
            
            return _safeTypes.Contains(type) || 
                   _safeTypes.Contains(underlyingType) ||
                   underlyingType.IsEnum;
        }
        
        /// <summary> 檢查是否為導航屬性 </summary>
        /// <param name="property">屬性</param>
        /// <param name="entityType">實體類型</param>
        /// <returns>是否為導航屬性</returns>
        private bool IsNavigationProperty(PropertyInfo property, Type entityType)
        {
            var propertyType = property.PropertyType;
            
            // 檢查是否為集合類型
            if (propertyType.IsGenericType && 
                typeof(System.Collections.IEnumerable).IsAssignableFrom(propertyType))
            {
                return true;
            }
            
            // 檢查是否為複雜實體類型（非基本類型且非安全類型）
            if (!IsSafeType(propertyType) && 
                propertyType.IsClass && 
                propertyType != typeof(string) &&
                propertyType != typeof(byte[]))
            {
                return true;
            }
            
            return false;
        }
        
        /// <summary> 提取實體ID </summary>
        /// <param name="entity">實體對象</param>
        /// <param name="metadata">實體元數據</param>
        /// <returns>實體ID</returns>
        private string ExtractEntityId(object entity, EntityMetadata metadata)
        {
            try
            {
                if (metadata.PrimaryKeyProperty != null)
                {
                    var value = metadata.PrimaryKeyProperty.GetValue(entity);
                    return value?.ToString() ?? string.Empty;
                }
                
                // 備用方法：尋找名稱包含ID的屬性
                var idProperty = metadata.SafeProperties
                    .FirstOrDefault(p => p.Name.EndsWith("ID", StringComparison.OrdinalIgnoreCase));
                
                if (idProperty != null)
                {
                    var value = idProperty.GetValue(entity);
                    return value?.ToString() ?? string.Empty;
                }
                
                return string.Empty;
            }
            catch
            {
                return string.Empty;
            }
        }
        
        /// <summary> 提取使用者ID </summary>
        /// <param name="entity">實體對象</param>
        /// <param name="metadata">實體元數據</param>
        /// <returns>使用者ID</returns>
        private string? ExtractUserId(object entity, EntityMetadata metadata)
        {
            try
            {
                if (metadata.UserIdProperty != null)
                {
                    var value = metadata.UserIdProperty.GetValue(entity);
                    return value?.ToString();
                }
                
                // 備用方法：尋找UpdateUserId屬性
                var userIdProperty = metadata.SafeProperties
                    .FirstOrDefault(p => p.Name.Equals("UpdateUserId", StringComparison.OrdinalIgnoreCase));
                
                if (userIdProperty != null)
                {
                    var value = userIdProperty.GetValue(entity);
                    return value?.ToString();
                }
                
                return null;
            }
            catch
            {
                return null;
            }
        }
        
        /// <summary> 轉換為安全值 </summary>
        /// <param name="value">原始值</param>
        /// <returns>安全值</returns>
        private object ConvertToSafeValue(object value)
        {
            return value switch
            {
                Guid guid => guid.ToString(),
                DateTime dateTime => dateTime.ToString("o"), // ISO 8601 格式
                DateTimeOffset dateTimeOffset => dateTimeOffset.ToString("o"),
                _ => value
            };
        }
        
        /// <summary> 計算變更的屬性 </summary>
        /// <param name="beforeValues">變更前的值</param>
        /// <param name="afterValues">變更後的值</param>
        /// <returns>變更的屬性名稱列表</returns>
        private List<string> CalculateChangedProperties(Dictionary<string, object>? beforeValues, Dictionary<string, object>? afterValues)
        {
            var changedProperties = new List<string>();
            
            if (beforeValues == null || afterValues == null)
                return changedProperties;
            
            foreach (var kvp in afterValues)
            {
                if (!beforeValues.TryGetValue(kvp.Key, out var beforeValue) ||
                    !Equals(beforeValue, kvp.Value))
                {
                    changedProperties.Add(kvp.Key);
                }
            }
            
            return changedProperties;
        }
        
        #endregion
    }
}
