# FastERP 後端日誌模組技術規格說明書

## 📋 **文件概述**

**文件版本：** 1.0  
**建立日期：** 2024年1月8日  
**適用範圍：** FastERP 後端日誌系統重構專案  

本文件詳細定義了 FastERP 後端日誌模組改善專案的技術規格，包括各組件的介面定義、資料結構、演算法實現和整合方式。

---

## 🏗️ **系統架構規格**

### **整體架構圖**
```
┌─────────────────┐    ┌──────────────────────┐    ┌─────────────────────┐
│   ERPDbContext  │───▶│ EnhancedEntityChange │───▶│ EnhancedLogFormatter│
│  SaveChangesAsync│    │      Tracker         │    │                     │
└─────────────────┘    └──────────────────────┘    └─────────────────────┘
                                    │                           │
                                    ▼                           ▼
                        ┌──────────────────────┐    ┌─────────────────────┐
                        │ EntityChangeSnapshot │    │      LogItem        │
                        └──────────────────────┘    └─────────────────────┘
                                                               │
                                                               ▼
                                                    ┌─────────────────────┐
                                                    │ MongoDBLoggerService│
                                                    │     (MongoDB)       │
                                                    └─────────────────────┘
```

### **核心組件職責**
1. **ERPDbContext** - 實體變更偵測和觸發點
2. **EnhancedEntityChangeTracker** - 變更資料捕獲和處理
3. **EnhancedLogFormatter** - 日誌格式化和結構化
4. **MongoDBLoggerService** - 日誌持久化存儲

---

## 📊 **資料結構規格**

### **EntityChangeSnapshot**
```csharp
/// <summary>
/// 實體變更快照 - 包含一次 SaveChanges 操作的所有變更
/// </summary>
public class EntityChangeSnapshot
{
    /// <summary> 交易唯一識別碼 </summary>
    public string TransactionId { get; set; }
    
    /// <summary> 變更記錄列表 </summary>
    public List<EntityChangeRecord> Changes { get; set; }
    
    /// <summary> 捕獲時間 </summary>
    public DateTime CaptureTime { get; set; }
    
    /// <summary> 是否有變更 </summary>
    public bool HasChanges => Changes?.Any() == true;
    
    /// <summary> 變更總數 </summary>
    public int TotalChanges => Changes?.Count ?? 0;
}
```

### **EntityChangeRecord**
```csharp
/// <summary>
/// 單一實體變更記錄
/// </summary>
public class EntityChangeRecord
{
    /// <summary> 實體類型名稱 </summary>
    public string EntityType { get; set; }
    
    /// <summary> 實體主鍵值 </summary>
    public string EntityId { get; set; }
    
    /// <summary> 變更操作類型 </summary>
    public EntityChangeOperation Operation { get; set; }
    
    /// <summary> 變更前的屬性值 </summary>
    public Dictionary<string, object>? BeforeValues { get; set; }
    
    /// <summary> 變更後的屬性值 </summary>
    public Dictionary<string, object>? AfterValues { get; set; }
    
    /// <summary> 變更的屬性名稱列表 </summary>
    public List<string> ChangedProperties { get; set; }
    
    /// <summary> 使用者ID </summary>
    public string? UserId { get; set; }
}
```

### **EntityChangeOperation**
```csharp
/// <summary>
/// 實體變更操作類型
/// </summary>
public enum EntityChangeOperation
{
    /// <summary> 新增 </summary>
    Create,
    
    /// <summary> 修改 </summary>
    Update,
    
    /// <summary> 刪除 </summary>
    Delete
}
```

### **LogItem**
```csharp
/// <summary>
/// 格式化後的日誌項目
/// </summary>
public class LogItem
{
    /// <summary> 操作類型 </summary>
    public string Operation { get; set; }
    
    /// <summary> 實體類型 </summary>
    public string? EntityType { get; set; }
    
    /// <summary> 實體ID </summary>
    public string? EntityId { get; set; }
    
    /// <summary> 日誌資料內容 </summary>
    public Dictionary<string, object> Data { get; set; }
    
    /// <summary> 摘要描述 </summary>
    public string Summary { get; set; }
}
```

---

## 🔧 **核心組件規格**

### **EnhancedEntityChangeTracker 規格**

#### **類別定義**
```csharp
/// <summary>
/// 增強型實體變更追蹤器
/// </summary>
public class EnhancedEntityChangeTracker
{
    #region Private Fields
    private static readonly ConcurrentDictionary<Type, EntityMetadata> _metadataCache = new();
    private static readonly HashSet<Type> _safeTypes = new()
    {
        typeof(string), typeof(int), typeof(long), typeof(decimal),
        typeof(bool), typeof(DateTime), typeof(DateTimeOffset),
        typeof(Guid), typeof(byte[]), typeof(float), typeof(double),
        typeof(short), typeof(byte), typeof(char)
    };
    #endregion
    
    #region Public Methods
    /// <summary> 捕獲實體變更快照 </summary>
    public EntityChangeSnapshot CaptureChanges(ChangeTracker changeTracker);
    
    /// <summary> 創建單一實體變更記錄 </summary>
    public EntityChangeRecord CreateChangeRecord(EntityEntry entry);
    
    /// <summary> 提取安全屬性值 </summary>
    public Dictionary<string, object> ExtractSafeProperties(object entity, EntityState state);
    #endregion
    
    #region Private Methods
    /// <summary> 獲取實體元數據 </summary>
    private EntityMetadata GetEntityMetadata(Type entityType);
    
    /// <summary> 檢查是否為安全類型 </summary>
    private bool IsSafeType(Type type);
    
    /// <summary> 檢查是否為導航屬性 </summary>
    private bool IsNavigationProperty(PropertyInfo property, Type entityType);
    #endregion
}
```

#### **關鍵演算法**

**安全屬性提取演算法：**
```csharp
public Dictionary<string, object> ExtractSafeProperties(object entity, EntityState state)
{
    var result = new Dictionary<string, object>();
    var entityType = entity.GetType();
    var metadata = GetEntityMetadata(entityType);
    
    foreach (var property in metadata.SafeProperties)
    {
        try
        {
            var value = property.GetValue(entity);
            if (value != null)
            {
                result[property.Name] = ConvertToSafeValue(value);
            }
        }
        catch (Exception ex)
        {
            result[property.Name] = $"[提取錯誤: {ex.Message}]";
        }
    }
    
    return result;
}
```

**循環引用防護演算法：**
```csharp
private bool IsNavigationProperty(PropertyInfo property, Type entityType)
{
    // 1. 檢查屬性類型是否為實體類型
    if (IsEntityType(property.PropertyType))
        return true;
    
    // 2. 檢查是否為集合類型且元素為實體類型
    if (IsCollectionOfEntities(property.PropertyType))
        return true;
    
    // 3. 檢查是否有 ForeignKey 或 InverseProperty 屬性
    if (HasNavigationAttributes(property))
        return true;
    
    return false;
}
```

### **EnhancedLogFormatter 規格**

#### **類別定義**
```csharp
/// <summary>
/// 增強型日誌格式化器
/// </summary>
public class EnhancedLogFormatter
{
    #region Public Methods
    /// <summary> 格式化變更快照為日誌項目列表 </summary>
    public List<LogItem> FormatSnapshot(EntityChangeSnapshot snapshot);
    
    /// <summary> 創建修改詳細資訊 </summary>
    public LogItem CreateModificationDetails(EntityChangeRecord record);
    
    /// <summary> 創建批次操作摘要 </summary>
    public LogItem CreateSummaryEntry(EntityChangeSnapshot snapshot);
    
    /// <summary> 創建錯誤日誌項目 </summary>
    public LogItem CreateErrorEntry(string error, string entityType);
    #endregion
    
    #region Private Methods
    /// <summary> 計算屬性差異 </summary>
    private Dictionary<string, PropertyChange> CalculatePropertyChanges(
        Dictionary<string, object>? before, 
        Dictionary<string, object>? after);
    
    /// <summary> 生成摘要描述 </summary>
    private string GenerateSummary(EntityChangeRecord record);
    #endregion
}
```

#### **日誌格式規範**

**CREATE 操作格式：**
```json
{
  "operation": "CREATE",
  "entityType": "Partner",
  "entityId": "12345678-1234-1234-1234-123456789012",
  "createdData": {
    "PartnerID": "12345678-1234-1234-1234-123456789012",
    "IsStop": false,
    "CreateTime": 1704067200000,
    "CreateUserId": "TEST_USER"
  },
  "propertyCount": 4,
  "summary": "新增 Partner 實體，包含 4 個屬性"
}
```

**UPDATE 操作格式：**
```json
{
  "operation": "UPDATE",
  "entityType": "Partner",
  "entityId": "12345678-1234-1234-1234-123456789012",
  "modifications": {
    "IsStop": {
      "before": false,
      "after": true,
      "changed": true
    },
    "UpdateTime": {
      "before": 1704067200000,
      "after": 1704067260000,
      "changed": true
    }
  },
  "changedProperties": ["IsStop", "UpdateTime"],
  "changeCount": 2,
  "summary": "修改 Partner 實體，變更 2 個屬性：IsStop, UpdateTime"
}
```

**DELETE 操作格式：**
```json
{
  "operation": "DELETE",
  "entityType": "Partner",
  "entityId": "12345678-1234-1234-1234-123456789012",
  "deletedData": {
    "PartnerID": "12345678-1234-1234-1234-123456789012",
    "IsStop": true,
    "CreateTime": 1704067200000,
    "UpdateTime": 1704067260000
  },
  "propertyCount": 4,
  "summary": "刪除 Partner 實體，記錄 4 個屬性的最終值"
}
```

**BATCH 操作摘要格式：**
```json
{
  "operation": "BATCH",
  "transactionId": "87654321-4321-4321-4321-210987654321",
  "totalChanges": 5,
  "operationCounts": {
    "CREATE": 2,
    "UPDATE": 2,
    "DELETE": 1
  },
  "entityTypeCounts": {
    "Partner": 3,
    "CustomerDetail": 1,
    "SupplierDetail": 1
  },
  "captureTime": "2024-01-01T12:00:00.000Z",
  "summary": "批次操作包含 5 個變更：2個新增、2個修改、1個刪除"
}
```

---

## 🔌 **整合規格**

### **ERPDbContext 整合**

#### **修改範圍**
- 檔案：`Models/ERPDbContext.cs`
- 方法：`SaveChangesAsync(CancellationToken cancellationToken = default)`

#### **整合實現**
```csharp
public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
{
    // 使用增強型追蹤器捕獲變更
    var changeSnapshot = _enhancedTracker.CaptureChanges(ChangeTracker);
    var transactionId = Guid.NewGuid().ToString();

    try
    {
        var result = await base.SaveChangesAsync(cancellationToken);

        if (result > 0 && changeSnapshot.HasChanges)
        {
            await LogEnhancedChangesAsync(changeSnapshot, transactionId);
        }

        return result;
    }
    catch (Exception ex)
    {
        if (_logger != null)
        {
            await _logger.LogErrorAsync(
                $"資料庫保存變更失敗，交易ID: {transactionId}",
                ex,
                "ERPDbContext"
            );
        }
        throw;
    }
}

private async Task LogEnhancedChangesAsync(EntityChangeSnapshot snapshot, string transactionId)
{
    if (_logger == null) return;

    try
    {
        var logItems = _enhancedFormatter.FormatSnapshot(snapshot);
        
        foreach (var logItem in logItems)
        {
            await _logger.LogDataAsync(
                logItem.Summary,
                logItem.Data,
                transactionId,
                "ERPDbContext"
            );
        }
    }
    catch (Exception ex)
    {
        Console.WriteLine($"[ERPDbContext] 增強型日誌記錄失敗: {ex.Message}");
    }
}
```

### **MongoDBLoggerService 整合**

#### **簡化實現**
```csharp
public async Task LogDataAsync<T>(string message, T changedData, string transactionId, string source = "System") 
    where T : class
{
    if (string.IsNullOrWhiteSpace(message)) return;

    // 直接使用已格式化的資料，無需額外序列化處理
    Dictionary<string, object>? dataDict = null;
    if (changedData is Dictionary<string, object> dict)
    {
        dataDict = dict;
    }
    else
    {
        // 備用序列化方法（應該很少使用）
        dataDict = ConvertToDictionary(changedData);
    }

    await LogAsync(LogLevelEnum.Debug, message, null, dataDict, transactionId, source);
}
```

---

## 📏 **效能規格**

### **效能目標**
- **單一實體變更處理時間：** < 50ms
- **批次操作處理時間：** < 200ms (10個實體)
- **記憶體使用量：** < 10MB (正常操作)
- **快取命中率：** > 90%

### **效能監控指標**
```csharp
/// <summary>
/// 效能監控指標
/// </summary>
public class PerformanceMetrics
{
    public TimeSpan ProcessingTime { get; set; }
    public long MemoryUsage { get; set; }
    public int CacheHitCount { get; set; }
    public int CacheMissCount { get; set; }
    public double CacheHitRate => CacheHitCount / (double)(CacheHitCount + CacheMissCount);
}
```

---

## 🧪 **測試規格**

### **單元測試覆蓋範圍**
- **EnhancedEntityChangeTracker：** > 90%
- **EnhancedLogFormatter：** > 90%
- **整合邏輯：** > 80%

### **測試案例分類**
1. **基本功能測試**
2. **循環引用防護測試**
3. **效能基準測試**
4. **錯誤處理測試**
5. **整合測試**

此技術規格說明書為 FastERP 後端日誌模組改善專案提供了詳細的實現指導，確保開發過程中的一致性和品質。
