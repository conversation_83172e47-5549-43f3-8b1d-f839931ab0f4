using System;
using System.Threading.Tasks;
using FAST_ERP_Backend.Interfaces.Common;
using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Ims;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace FAST_ERP_Backend.Controllers.Common
{
    /// <summary>
    /// 日誌修復測試控制器
    /// 用於測試修復後的日誌系統是否能正確記錄實體變更
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class LoggingFixTestController : ControllerBase
    {
        #region Private Fields

        private readonly ERPDbContext _context;
        private readonly IResilientLoggerService _resilientLogger;
        private readonly ILogDataProcessor _dataProcessor;
        private readonly ILogger<LoggingFixTestController> _logger;

        #endregion

        #region Constructor

        /// <summary>
        /// 初始化測試控制器
        /// </summary>
        public LoggingFixTestController(
            ERPDbContext context,
            IResilientLoggerService resilientLogger,
            ILogDataProcessor dataProcessor,
            ILogger<LoggingFixTestController> logger)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _resilientLogger = resilientLogger ?? throw new ArgumentNullException(nameof(resilientLogger));
            _dataProcessor = dataProcessor ?? throw new ArgumentNullException(nameof(dataProcessor));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        #endregion

        #region Test Endpoints

        /// <summary> 測試簡單實體變更 </summary>
        [HttpPost("test-simple-entity-change")]
        public async Task<IActionResult> TestSimpleEntityChange()
        {
            try
            {
                // 創建一個簡單的 Partner 實體
                var partner = new Partner
                {
                    PartnerID = Guid.NewGuid(),
                    IsStop = false,
                    CreateTime = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds(),
                    CreateUserId = "TEST_USER",
                    UpdateTime = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds(),
                    UpdateUserId = "TEST_USER"
                };

                _context.Ims_Partner.Add(partner);
                var result = await _context.SaveChangesAsync();

                return Ok(new
                {
                    Success = true,
                    Message = "簡單實體變更測試完成",
                    PartnerID = partner.PartnerID,
                    AffectedRows = result,
                    Timestamp = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "簡單實體變更測試失敗");
                return StatusCode(500, new { Error = ex.Message });
            }
        }

        /// <summary> 測試複雜實體變更（包含導航屬性） </summary>
        [HttpPost("test-complex-entity-change")]
        public async Task<IActionResult> TestComplexEntityChange()
        {
            try
            {
                var partnerId = Guid.NewGuid();

                // 創建 Partner 及其相關實體
                var partner = new Partner
                {
                    PartnerID = partnerId,
                    IsStop = false,
                    CreateTime = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds(),
                    CreateUserId = "TEST_USER",
                    UpdateTime = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds(),
                    UpdateUserId = "TEST_USER"
                };

                var individualDetail = new IndividualDetail
                {
                    PartnerID = partnerId,
                    LastName = "測試",
                    FirstName = "用戶",
                    IdentificationNumber = "A123456789",
                    BirthDate = DateTimeOffset.UtcNow.AddYears(-30).ToUnixTimeMilliseconds(),
                    CreateTime = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds(),
                    CreateUserId = "TEST_USER",
                    UpdateTime = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds(),
                    UpdateUserId = "TEST_USER"
                };

                var customerDetail = new CustomerDetail
                {
                    PartnerID = partnerId,
                    CustomerCode = "TEST001",
                    SettlementDay = 30,
                    CreateTime = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds(),
                    CreateUserId = "TEST_USER",
                    UpdateTime = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds(),
                    UpdateUserId = "TEST_USER"
                };

                _context.Ims_Partner.Add(partner);
                _context.Ims_IndividualDetail.Add(individualDetail);
                _context.Ims_CustomerDetail.Add(customerDetail);

                var result = await _context.SaveChangesAsync();

                return Ok(new
                {
                    Success = true,
                    Message = "複雜實體變更測試完成",
                    PartnerID = partnerId,
                    AffectedRows = result,
                    Timestamp = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "複雜實體變更測試失敗");
                return StatusCode(500, new { Error = ex.Message });
            }
        }

        /// <summary> 測試實體更新 </summary>
        [HttpPut("test-entity-update/{partnerId}")]
        public async Task<IActionResult> TestEntityUpdate(Guid partnerId)
        {
            try
            {
                var partner = await _context.Ims_Partner.FindAsync(partnerId);
                if (partner == null)
                {
                    return NotFound(new { Message = "找不到指定的 Partner" });
                }

                // 修改實體屬性
                partner.UpdateTime = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
                partner.UpdateUserId = "UPDATED_USER";

                var result = await _context.SaveChangesAsync();

                return Ok(new
                {
                    Success = true,
                    Message = "實體更新測試完成",
                    PartnerID = partnerId,
                    AffectedRows = result,
                    Timestamp = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "實體更新測試失敗");
                return StatusCode(500, new { Error = ex.Message });
            }
        }

        /// <summary> 測試批次實體變更 </summary>
        [HttpPost("test-batch-entity-changes")]
        public async Task<IActionResult> TestBatchEntityChanges()
        {
            try
            {
                var partnerId1 = Guid.NewGuid();
                var partnerId2 = Guid.NewGuid();

                // 創建多個實體
                var partner1 = new Partner
                {
                    PartnerID = partnerId1,
                    IsStop = false,
                    CreateTime = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds(),
                    CreateUserId = "BATCH_TEST_USER",
                    UpdateTime = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds(),
                    UpdateUserId = "BATCH_TEST_USER"
                };

                var partner2 = new Partner
                {
                    PartnerID = partnerId2,
                    IsStop = false,
                    CreateTime = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds(),
                    CreateUserId = "BATCH_TEST_USER",
                    UpdateTime = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds(),
                    UpdateUserId = "BATCH_TEST_USER"
                };

                var individualDetail1 = new IndividualDetail
                {
                    PartnerID = partnerId1,
                    LastName = "批次測試1",
                    FirstName = "用戶",
                    IdentificationNumber = "B123456789",
                    BirthDate = DateTimeOffset.UtcNow.AddYears(-25).ToUnixTimeMilliseconds(),
                    CreateTime = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds(),
                    CreateUserId = "BATCH_TEST_USER",
                    UpdateTime = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds(),
                    UpdateUserId = "BATCH_TEST_USER"
                };

                var individualDetail2 = new IndividualDetail
                {
                    PartnerID = partnerId2,
                    LastName = "批次測試2",
                    FirstName = "用戶",
                    IdentificationNumber = "C123456789",
                    BirthDate = DateTimeOffset.UtcNow.AddYears(-35).ToUnixTimeMilliseconds(),
                    CreateTime = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds(),
                    CreateUserId = "BATCH_TEST_USER",
                    UpdateTime = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds(),
                    UpdateUserId = "BATCH_TEST_USER"
                };

                _context.Ims_Partner.AddRange(partner1, partner2);
                _context.Ims_IndividualDetail.AddRange(individualDetail1, individualDetail2);

                var result = await _context.SaveChangesAsync();

                return Ok(new
                {
                    Success = true,
                    Message = "批次實體變更測試完成",
                    PartnerIDs = new[] { partnerId1, partnerId2 },
                    AffectedRows = result,
                    Timestamp = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批次實體變更測試失敗");
                return StatusCode(500, new { Error = ex.Message });
            }
        }

        /// <summary> 測試直接使用新版日誌服務 </summary>
        [HttpPost("test-direct-logging")]
        public async Task<IActionResult> TestDirectLogging()
        {
            try
            {
                // 測試資料
                var testData = new
                {
                    TestId = Guid.NewGuid(),
                    TestName = "直接日誌測試",
                    TestTime = DateTime.UtcNow,
                    TestData = new
                    {
                        Property1 = "值1",
                        Property2 = 123,
                        Property3 = true
                    }
                };

                // 使用資料處理器序列化
                var serializedData = _dataProcessor.SafeSerialize(testData);

                // 記錄日誌
                await _resilientLogger.LogInfoAsync($"直接日誌測試: {serializedData}", "LoggingFixTest");

                return Ok(new
                {
                    Success = true,
                    Message = "直接日誌測試完成",
                    SerializedData = serializedData,
                    Timestamp = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "直接日誌測試失敗");
                return StatusCode(500, new { Error = ex.Message });
            }
        }

        /// <summary> 獲取日誌系統健康狀態 </summary>
        [HttpGet("health-status")]
        public async Task<IActionResult> GetHealthStatus()
        {
            try
            {
                var healthStatus = await _resilientLogger.GetHealthStatusAsync();
                var errorStatistics = await _resilientLogger.GetErrorStatisticsAsync();

                return Ok(new
                {
                    HealthStatus = healthStatus,
                    ErrorStatistics = errorStatistics,
                    Timestamp = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "獲取健康狀態失敗");
                return StatusCode(500, new { Error = ex.Message });
            }
        }

        #endregion
    }
}
