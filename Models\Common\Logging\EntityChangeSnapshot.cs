using System;
using System.Collections.Generic;
using System.Linq;

namespace FAST_ERP_Backend.Models.Common.Logging
{
    /// <summary>
    /// 實體變更快照 - 包含一次 SaveChanges 操作的所有變更
    /// </summary>
    public class EntityChangeSnapshot
    {
        /// <summary> 交易唯一識別碼 </summary>
        public string TransactionId { get; set; } = string.Empty;
        
        /// <summary> 變更記錄列表 </summary>
        public List<EntityChangeRecord> Changes { get; set; } = new List<EntityChangeRecord>();
        
        /// <summary> 捕獲時間 </summary>
        public DateTime CaptureTime { get; set; } = DateTime.UtcNow;
        
        /// <summary> 是否有變更 </summary>
        public bool HasChanges => Changes?.Any() == true;
        
        /// <summary> 變更總數 </summary>
        public int TotalChanges => Changes?.Count ?? 0;
        
        /// <summary> 建構式 </summary>
        public EntityChangeSnapshot()
        {
            TransactionId = Guid.NewGuid().ToString();
            Changes = new List<EntityChangeRecord>();
            CaptureTime = DateTime.UtcNow;
        }
        
        /// <summary> 建構式 </summary>
        /// <param name="transactionId">交易ID</param>
        public EntityChangeSnapshot(string transactionId)
        {
            TransactionId = transactionId;
            Changes = new List<EntityChangeRecord>();
            CaptureTime = DateTime.UtcNow;
        }
    }
}
