using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;
using FAST_ERP_Backend.Interfaces.Common;
using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Common;
using FAST_ERP_Backend.Models.Common.Logging;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using MongoDB.Driver;
using MongoDB.Bson;

namespace FAST_ERP_Backend.Services.Common
{
    /// <summary>  MongoDB日誌服務實現 </summary>
    public class MongoDBLoggerService : ILoggerService
    {
        #region Private Fields
        private readonly IMongoCollection<ILogEntry> _logCollection;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly ILogger<MongoDBLoggerService> _systemLogger;
        #endregion

        #region Constructor
        /// <summary> 構造函數 </summary>
        /// <param name="configuration">配置服務</param>
        /// <param name="httpContextAccessor">HTTP上下文訪問器</param>
        /// <param name="systemLogger">系統日誌記錄器</param>
        public MongoDBLoggerService(IConfiguration configuration, IHttpContextAccessor httpContextAccessor, ILogger<MongoDBLoggerService> systemLogger)
        {
            _httpContextAccessor = httpContextAccessor ?? throw new ArgumentNullException(nameof(httpContextAccessor));
            _systemLogger = systemLogger ?? throw new ArgumentNullException(nameof(systemLogger));

            try
            {
                var connectionString = configuration["MongoDB:ConnectionString"] ?? throw new InvalidOperationException("MongoDB連接字符串未配置");
                var databaseName = configuration["MongoDB:DatabaseName"] ?? throw new InvalidOperationException("MongoDB數據庫名稱未配置");
                var collectionName = configuration["MongoDB:CollectionName"] ?? throw new InvalidOperationException("MongoDB集合名稱未配置");

                var client = new MongoClient(connectionString);
                var database = client.GetDatabase(databaseName);
                _logCollection = database.GetCollection<ILogEntry>(collectionName);

                // MongoDB 日誌服務已初始化

                _systemLogger.LogInformation("MongoDB日誌服務初始化成功");
            }
            catch (Exception ex)
            {
                _systemLogger.LogError(ex, "MongoDB日誌服務初始化失敗");
                throw;
            }
        }
        #endregion

        #region Public Logging Methods
        /// <summary> 記錄調試日誌 </summary>
        /// <param name="message">日誌消息</param>
        /// <param name="source">來源</param>
        /// <returns>異步任務</returns>
        public async Task LogDebugAsync(string message, string source = "System")
        {
            if (string.IsNullOrWhiteSpace(message)) return;

            await LogAsync(LogLevelEnum.Debug, message, null, null, GenerateTransactionId(), source);
        }

        /// <summary> 記錄信息日誌 </summary>
        /// <param name="message">日誌消息</param>
        /// <param name="source">來源</param>
        /// <returns>異步任務</returns>
        public async Task LogInfoAsync(string message, string source = "System")
        {
            if (string.IsNullOrWhiteSpace(message)) return;

            await LogAsync(LogLevelEnum.Information, message, null, null, GenerateTransactionId(), source);
        }

        /// <summary> 記錄警告日誌 </summary>
        /// <param name="message">日誌消息</param>
        /// <param name="source">來源</param>
        /// <returns>異步任務</returns>
        public async Task LogWarningAsync(string message, string source = "System")
        {
            if (string.IsNullOrWhiteSpace(message)) return;

            await LogAsync(LogLevelEnum.Warning, message, null, null, GenerateTransactionId(), source);
        }

        /// <summary> 記錄錯誤日誌 </summary>
        /// <param name="message">日誌消息</param>
        /// <param name="exception">異常信息</param>
        /// <param name="source">來源</param>
        /// <returns>異步任務</returns>
        public async Task LogErrorAsync(string message, Exception? exception = null, string source = "System")
        {
            if (string.IsNullOrWhiteSpace(message)) return;

            await LogAsync(LogLevelEnum.Error, message, exception, null, GenerateTransactionId(), source);
        }

        /// <summary> 記錄異動日誌 </summary>
        /// <typeparam name="T">資料型別</typeparam>
        /// <param name="message">日誌消息</param>
        /// <param name="changedData">異動資料</param>
        /// <param name="transactionId">交易ID</param>
        /// <param name="source">來源</param>
        /// <returns>異步任務</returns>
        public async Task LogDataAsync<T>(string message, T changedData, string transactionId, string source = "System") where T : class
        {
            if (string.IsNullOrWhiteSpace(message)) return;

            // 使用安全的序列化方法
            Dictionary<string, object>? dataDict = null;
            try
            {
                if (changedData != null)
                {
                    dataDict = ConvertToDictionary(changedData);
                }
            }
            catch (Exception ex)
            {
                dataDict = new Dictionary<string, object>
                {
                    ["serialization_error"] = ex.Message,
                    ["original_type"] = changedData?.GetType().Name ?? "Unknown"
                };
                _systemLogger.LogWarning("資料序列化失敗: {Error}", ex.Message);
            }

            await LogAsync(LogLevelEnum.Debug, message, null, dataDict, transactionId, source);
        }
        #endregion

        #region Private Core Methods
        /// <summary> 記錄日誌的核心方法 </summary>
        /// <param name="level">日誌級別</param>
        /// <param name="message">日誌消息</param>
        /// <param name="exception">異常信息</param>
        /// <param name="changedData">異動資料</param>
        /// <param name="transactionId">交易ID</param>
        /// <param name="source">來源</param>
        /// <returns>異步任務</returns>
        private async Task LogAsync(LogLevelEnum level, string message, Exception? exception, Dictionary<string, object?>? changedData, string transactionId, string source)
        {
            try
            {
                var contextInfo = ExtractHttpContextInfo();
                var logEntry = CreateLogEntry(level, message, exception, changedData, transactionId, source, contextInfo);

                await _logCollection.InsertOneAsync(logEntry);

                // 僅在調試模式下輸出詳細信息
                _systemLogger.LogDebug("日誌記錄成功: {Level} - {Message}", level, message);
            }
            catch (Exception ex)
            {
                // 使用系統日誌記錄失敗信息，避免遞歸
                _systemLogger.LogError(ex, "MongoDB日誌記錄失敗: {Level} - {Message}", level, message);

                // 不要重新拋出異常，避免影響主要業務流程
                // throw; // 註解掉這行，讓日誌失敗不影響業務操作
            }
        }

        /// <summary> 提取HTTP上下文信息 </summary>
        /// <returns>HTTP上下文信息</returns>
        private HttpContextInfo ExtractHttpContextInfo()
        {
            var context = _httpContextAccessor.HttpContext;
            if (context == null)
            {
                return new HttpContextInfo();
            }

            var contextInfo = new HttpContextInfo
            {
                UserId = context.User?.Identity?.Name,
                IpAddress = context.Connection.RemoteIpAddress?.ToString(),
                RequestUrl = $"{context.Request.Scheme}://{context.Request.Host}{context.Request.Path}{context.Request.QueryString}"
            };

            // 安全地獲取User-Agent
            if (context.Request.Headers.TryGetValue("User-Agent", out var userAgentValues))
            {
                contextInfo.UserAgent = userAgentValues.ToString();
            }

            return contextInfo;
        }

        /// <summary> 創建日誌條目 </summary>
        /// <param name="level">日誌級別</param>
        /// <param name="message">日誌消息</param>
        /// <param name="exception">異常信息</param>
        /// <param name="changedData">異動資料</param>
        /// <param name="transactionId">交易ID</param>
        /// <param name="source">來源</param>
        /// <param name="contextInfo">HTTP上下文信息</param>
        /// <returns>日誌條目</returns>
        private MongoDBLogEntry CreateLogEntry(LogLevelEnum level, string message, Exception? exception,
            Dictionary<string, object?>? changedData, string transactionId, string source, HttpContextInfo contextInfo)
        {
            var logEntry = new MongoDBLogEntry
            {
                Level = level.ToString(),
                TransactionId = transactionId,
                Message = message,
                Data = changedData, // 直接使用已經安全序列化的資料
                Source = source,
                Exception = exception?.Message,
                StackTrace = exception?.StackTrace,
                UserId = contextInfo.UserId,
                IpAddress = contextInfo.IpAddress,
                UserAgent = contextInfo.UserAgent,
                RequestUrl = contextInfo.RequestUrl
            };

            return logEntry;
        }

        /// <summary> 生成交易ID </summary>
        /// <returns>新的交易ID</returns>
        private static string GenerateTransactionId()
        {
            return Guid.NewGuid().ToString();
        }
        #endregion

        #region Data Processing Methods
        
        /// <summary> 準備數據以便序列化，優化處理效率並移除不必要的元數據 </summary>
        /// <param name="data">要序列化的數據</param>
        /// <returns>序列化後的數據</returns>
        /// <remarks>將 Guid 轉為字符串以避免序列化問題，並處理嵌套字典以移除不必要的序列化元數據</remarks>
        private static Dictionary<string, object>? PrepareDataForSerialization(Dictionary<string, object>? data)
        {
            if (data == null || data.Count == 0)
            {
                return null;
            }

            var result = new Dictionary<string, object>(data.Count);
            foreach (var kvp in data)
            {
                if (kvp.Value == null)
                {
                    continue; // 跳過 null 值
                }

                result[kvp.Key] = ProcessValue(kvp.Value);
            }

            return result.Count > 0 ? result : null;
        }

        /// <summary> 處理單個值的序列化 </summary>
        /// <param name="value">要處理的值</param>
        /// <returns>處理後的值</returns>
        private static object ProcessValue(object value)
        {
            return value switch
            {
                Guid guid => guid.ToString(),
                Dictionary<string, object> nestedDict => ProcessNestedDictionary(nestedDict),
                DateTime dateTime => dateTime.ToString("o"), // 使用 ISO 8601 格式，更標準
                DateTimeOffset dateTimeOffset => dateTimeOffset.ToString("o"),
                _ => value
            };
        }

        /// <summary> 處理嵌套字典 </summary>
        /// <param name="nestedDict">嵌套字典</param>
        /// <returns>處理後的字典</returns>
        private static object ProcessNestedDictionary(Dictionary<string, object> nestedDict)
        {
            // 檢查是否有序列化元數據並處理
            if (nestedDict.ContainsKey("_v") && nestedDict["_v"] is Dictionary<string, object> valueDict)
            {
                return PrepareDataForSerialization(valueDict);
            }

            return PrepareDataForSerialization(nestedDict);
        }

        /// <summary> 轉換可空字典為非可空字典 </summary>
        /// <param name="nullableDict">可空字典</param>
        /// <returns>非可空字典</returns>
        private static Dictionary<string, object> ConvertToNonNullableDictionary(Dictionary<string, object?> nullableDict)
        {
            var result = new Dictionary<string, object>();

            foreach (var kvp in nullableDict)
            {
                if (kvp.Value != null)
                {
                    result[kvp.Key] = kvp.Value;
                }
                else
                {
                    // 保留 null 值作為字符串，以便在日誌中可見
                    result[kvp.Key] = "[null]";
                }
            }

            return result;
        }

        /// <summary> 將對象轉換為字典，處理嵌套對象以優化日誌數據處理 </summary>
        /// <param name="obj">要轉換的對象</param>
        /// <returns>轉換後的字典</returns>
        private Dictionary<string, object>? ConvertToDictionary(object? obj)
        {
            // 使用 ReferenceEqualityComparer 防止對實值型別的 boxing/unboxing 產生誤判
            return ConvertToDictionaryByReflection(obj, new HashSet<object>(ReferenceEqualityComparer.Instance), 0);
        }

        /// <summary> 使用反射將對象轉換為字典，處理嵌套對象並防止循環引用 </summary>
        /// <param name="obj">要轉換的對象</param>
        /// <param name="visitedObjects">已訪問的對象集合，用於檢測循環引用</param>
        /// <param name="depth">當前遞迴深度</param>
        /// <returns>轉換後的字典</returns>
        private Dictionary<string, object>? ConvertToDictionaryByReflection(object? obj, HashSet<object> visitedObjects, int depth)
        {
            if (obj == null)
            {
                return null;
            }

            // 防止過深的遞迴，避免 StackOverflowException
            const int maxDepth = 5;
            if (depth >= maxDepth)
            {
                return new Dictionary<string, object> { { "_truncated", $"[達到最大深度 {maxDepth}]" } };
            }

            var type = obj.GetType();

            // 對於簡單類型，直接返回值，避免不必要的反射
            if (IsSimpleType(type))
            {
                return new Dictionary<string, object> { { "value", obj } };
            }

            // 檢測循環引用
            if (!visitedObjects.Add(obj))
            {
                return new Dictionary<string, object> { { "_circular", $"[循環引用: {type.Name}]" } };
            }

            try
            {
                var dict = new Dictionary<string, object>();
                var properties = type.GetProperties(BindingFlags.Public | BindingFlags.Instance);

                foreach (var prop in properties)
                {
                    // 跳過索引器屬性
                    if (prop.GetIndexParameters().Length > 0)
                    {
                        continue;
                    }

                    // 強化導航屬性過濾
                    if (IsNavigationProperty(prop, type))
                    {
                        dict[prop.Name] = "[導航屬性已跳過]";
                        continue;
                    }

                    object? value;
                    try
                    {
                        value = prop.GetValue(obj);
                    }
                    catch (Exception ex)
                    {
                        // 若讀取屬性失敗，記錄錯誤信息，而不是讓整個日誌崩潰
                        dict[prop.Name] = $"[反射讀取錯誤: {ex.Message}]";
                        continue;
                    }

                    if (value == null)
                    {
                        continue;
                    }

                    dict[prop.Name] = ConvertPropertyValueByReflection(value, visitedObjects, depth + 1);
                }

                return dict.Count > 0 ? dict : null;
            }
            finally
            {
                // 從已訪問集合中移除當前對象，允許在其他分支中重新訪問
                visitedObjects.Remove(obj);
            }
        }

        /// <summary> 使用反射轉換屬性值，處理不同類型的值 </summary>
        /// <param name="value">屬性值</param>
        /// <param name="visitedObjects">已訪問的對象集合</param>
        /// <param name="depth">當前遞迴深度</param>
        /// <returns>轉換後的值</returns>
        private object ConvertPropertyValueByReflection(object value, HashSet<object> visitedObjects, int depth)
        {
            var type = value.GetType();

            // 處理基本類型和常見類型
            if (IsSimpleType(type))
            {
                return value;
            }

            // 處理集合類型
            if (value is System.Collections.IEnumerable enumerable && value is not string)
            {
                var list = new List<object>();
                int count = 0;
                const int maxItems = 10; // 限制集合項目數量

                foreach (var item in enumerable)
                {
                    if (count >= maxItems)
                    {
                        list.Add($"[還有更多項目，已截斷於 {maxItems} 項]");
                        break;
                    }

                    if (item != null)
                    {
                        list.Add(ConvertPropertyValueByReflection(item, visitedObjects, depth + 1));
                    }
                    count++;
                }

                return list;
            }

            // 遞歸處理複雜對象
            return ConvertToDictionaryByReflection(value, visitedObjects, depth) ?? value;
        }

        /// <summary> 檢查是否為 EF Core 導航屬性 (強化版) </summary>
        /// <param name="property">屬性資訊</param>
        /// <param name="declaringType">宣告該屬性的類型</param>
        /// <returns>是否為導航屬性</returns>
        private static bool IsNavigationProperty(PropertyInfo property, Type declaringType)
        {
            // 如果屬性類型和宣告類型相同，則很可能是自我引用，應跳過
            if (property.PropertyType == declaringType)
            {
                return true;
            }

            // 檢查是否有 ForeignKey 或 InverseProperty 屬性
            if (property.IsDefined(typeof(System.ComponentModel.DataAnnotations.Schema.ForeignKeyAttribute), false) ||
                property.IsDefined(typeof(System.ComponentModel.DataAnnotations.Schema.InversePropertyAttribute), false))
            {
                return true;
            }

            // 檢查是否為虛擬屬性，這是 EF Core 延遲載入的標誌
            var getMethod = property.GetGetMethod(true); // 包含非 public getter
            if (getMethod != null && getMethod.IsVirtual && !getMethod.IsFinal)
            {
                var propertyType = property.PropertyType;

                // 檢查是否為實體類型或實體集合
                if (IsEntityType(propertyType))
                {
                    return true;
                }

                if (propertyType.IsGenericType && typeof(System.Collections.IEnumerable).IsAssignableFrom(propertyType))
                {
                    var genericType = propertyType.GetGenericArguments().FirstOrDefault();
                    if (genericType != null && IsEntityType(genericType))
                    {
                        return true;
                    }
                }
            }

            return false;
        }

        /// <summary> 檢查是否為實體類型 (基於 ModelBaseEntity) </summary>
        /// <param name="type">要檢查的類型</param>
        /// <returns>是否為實體類型</returns>
        private static bool IsEntityType(Type type)
        {
            // 核心規則：檢查類型是否繼承自 ModelBaseEntity
            if (typeof(ModelBaseEntity).IsAssignableFrom(type))
            {
                return true;
            }

            // 輔助規則：檢查是否有 Key 屬性
            if (type.GetProperties().Any(p => p.IsDefined(typeof(System.ComponentModel.DataAnnotations.KeyAttribute), false)))
            {
                return true;
            }
            
            return false;
        }

        /// <summary> 檢查是否為簡單類型 </summary>
        /// <param name="type">類型</param>
        /// <returns>是否為簡單類型</returns>
        private static bool IsSimpleType(Type type)
        {
            return type.IsPrimitive ||
                   type.IsEnum ||
                   type == typeof(string) ||
                   type == typeof(decimal) ||
                   type == typeof(DateTime) ||
                   type == typeof(DateTimeOffset) ||
                   type == typeof(Guid) ||
                   type == typeof(TimeSpan);
        }
        #endregion
    }

    /// <summary>  HTTP上下文信息輔助類 -  </summary>
    internal class HttpContextInfo
    {
        public string? UserId { get; set; }
        public string? IpAddress { get; set; }
        public string? UserAgent { get; set; }
        public string? RequestUrl { get; set; }
    }
}