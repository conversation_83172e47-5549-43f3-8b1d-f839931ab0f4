using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Models.Common.Logging
{
    /// <summary>
    /// 實體變更操作類型
    /// </summary>
    public enum EntityChangeOperation
    {
        /// <summary> 新增 </summary>
        Create,
        
        /// <summary> 修改 </summary>
        Update,
        
        /// <summary> 刪除 </summary>
        Delete
    }

    /// <summary>
    /// 單一實體變更記錄
    /// </summary>
    public class EntityChangeRecord
    {
        /// <summary> 實體類型名稱 </summary>
        public string EntityType { get; set; } = string.Empty;
        
        /// <summary> 實體主鍵值 </summary>
        public string EntityId { get; set; } = string.Empty;
        
        /// <summary> 變更操作類型 </summary>
        public EntityChangeOperation Operation { get; set; }
        
        /// <summary> 變更前的屬性值 </summary>
        public Dictionary<string, object>? BeforeValues { get; set; }
        
        /// <summary> 變更後的屬性值 </summary>
        public Dictionary<string, object>? AfterValues { get; set; }
        
        /// <summary> 變更的屬性名稱列表 </summary>
        public List<string> ChangedProperties { get; set; } = new List<string>();
        
        /// <summary> 使用者ID </summary>
        public string? UserId { get; set; }
        
        /// <summary> 建構式 </summary>
        public EntityChangeRecord()
        {
            EntityType = string.Empty;
            EntityId = string.Empty;
            ChangedProperties = new List<string>();
        }
        
        /// <summary> 建構式 </summary>
        /// <param name="entityType">實體類型</param>
        /// <param name="entityId">實體ID</param>
        /// <param name="operation">操作類型</param>
        public EntityChangeRecord(string entityType, string entityId, EntityChangeOperation operation)
        {
            EntityType = entityType;
            EntityId = entityId;
            Operation = operation;
            ChangedProperties = new List<string>();
        }
    }
}
