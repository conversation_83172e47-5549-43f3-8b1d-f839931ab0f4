using System;
using System.Threading.Tasks;
using MongoDB.Driver;

namespace FAST_ERP_Backend.Interfaces.Common
{
    /// <summary>
    /// MongoDB 連接管理器介面
    /// 提供連接池管理、健康檢查和重連機制
    /// </summary>
    public interface IMongoDBConnectionManager
    {
        /// <summary> 獲取指定集合的連接 </summary>
        /// <typeparam name="T">集合文檔類型</typeparam>
        /// <param name="collectionName">集合名稱</param>
        /// <returns>MongoDB 集合實例</returns>
        Task<IMongoCollection<T>> GetCollectionAsync<T>(string collectionName);

        /// <summary> 執行連接健康檢查 </summary>
        /// <returns>連接是否健康</returns>
        Task<bool> HealthCheckAsync();

        /// <summary> 重置連接 </summary>
        /// <returns>重置是否成功</returns>
        Task<bool> ResetConnectionAsync();

        /// <summary> 獲取連接狀態資訊 </summary>
        /// <returns>連接狀態</returns>
        Task<MongoDBConnectionStatus> GetConnectionStatusAsync();

        /// <summary> 測試連接並執行簡單操作 </summary>
        /// <returns>測試結果</returns>
        Task<bool> TestConnectionAsync();
    }

    /// <summary>
    /// MongoDB 連接狀態
    /// </summary>
    public class MongoDBConnectionStatus
    {
        /// <summary> 連接是否可用 </summary>
        public bool IsConnected { get; set; }

        /// <summary> 最後連接時間 </summary>
        public DateTime LastConnectedTime { get; set; }

        /// <summary> 連接錯誤訊息 </summary>
        public string? ErrorMessage { get; set; }

        /// <summary> 重試次數 </summary>
        public int RetryCount { get; set; }

        /// <summary> 連接延遲（毫秒） </summary>
        public long LatencyMs { get; set; }

        /// <summary> 資料庫名稱 </summary>
        public string? DatabaseName { get; set; }

        /// <summary> 伺服器版本 </summary>
        public string? ServerVersion { get; set; }
    }
}
