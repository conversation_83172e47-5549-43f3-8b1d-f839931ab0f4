using System;
using System.Collections.Generic;

namespace FAST_ERP_Backend.Models.Common.Logging
{
    /// <summary>
    /// 格式化後的日誌項目
    /// </summary>
    public class LogItem
    {
        /// <summary> 操作類型 </summary>
        public string Operation { get; set; } = string.Empty;
        
        /// <summary> 實體類型 </summary>
        public string? EntityType { get; set; }
        
        /// <summary> 實體ID </summary>
        public string? EntityId { get; set; }
        
        /// <summary> 日誌資料內容 </summary>
        public Dictionary<string, object> Data { get; set; } = new Dictionary<string, object>();
        
        /// <summary> 摘要描述 </summary>
        public string Summary { get; set; } = string.Empty;
        
        /// <summary> 建構式 </summary>
        public LogItem()
        {
            Operation = string.Empty;
            Data = new Dictionary<string, object>();
            Summary = string.Empty;
        }
        
        /// <summary> 建構式 </summary>
        /// <param name="operation">操作類型</param>
        /// <param name="summary">摘要描述</param>
        public LogItem(string operation, string summary)
        {
            Operation = operation;
            Summary = summary;
            Data = new Dictionary<string, object>();
        }
    }

    /// <summary>
    /// 屬性變更詳細資訊
    /// </summary>
    public class PropertyChange
    {
        /// <summary> 變更前的值 </summary>
        public object? Before { get; set; }
        
        /// <summary> 變更後的值 </summary>
        public object? After { get; set; }
        
        /// <summary> 是否有變更 </summary>
        public bool Changed { get; set; }
        
        /// <summary> 建構式 </summary>
        public PropertyChange()
        {
            Changed = false;
        }
        
        /// <summary> 建構式 </summary>
        /// <param name="before">變更前的值</param>
        /// <param name="after">變更後的值</param>
        public PropertyChange(object? before, object? after)
        {
            Before = before;
            After = after;
            Changed = !Equals(before, after);
        }
    }
}
