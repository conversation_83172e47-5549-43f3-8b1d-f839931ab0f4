using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text.Json;
using FAST_ERP_Backend.Interfaces.Common;
using FAST_ERP_Backend.Models.Common.Logging;
using Microsoft.Extensions.Logging;

namespace FAST_ERP_Backend.Services.Common
{
    /// <summary>
    /// 日誌資料處理器實現
    /// 將複雜的實體變更轉換為簡化、可讀的日誌格式
    /// </summary>
    public class LogDataProcessor : ILogDataProcessor
    {
        #region Private Fields

        private readonly ILogger<LogDataProcessor> _logger;
        private readonly LogProcessingOptions _options;
        private readonly JsonSerializerOptions _jsonOptions;

        // 安全類型快取
        private static readonly HashSet<Type> _safeTypes = new HashSet<Type>
        {
            typeof(string), typeof(int), typeof(long), typeof(decimal), typeof(bool),
            typeof(DateTime), typeof(DateTimeOffset), typeof(Guid), typeof(byte[]),
            typeof(float), typeof(double), typeof(short), typeof(byte), typeof(char),
            // 可空類型
            typeof(int?), typeof(long?), typeof(decimal?), typeof(bool?),
            typeof(DateTime?), typeof(DateTimeOffset?), typeof(Guid?),
            typeof(float?), typeof(double?), typeof(short?), typeof(byte?), typeof(char?)
        };

        #endregion

        #region Constructor

        /// <summary>
        /// 初始化日誌資料處理器
        /// </summary>
        /// <param name="logger">日誌服務</param>
        /// <param name="options">處理選項</param>
        public LogDataProcessor(ILogger<LogDataProcessor> logger, LogProcessingOptions? options = null)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _options = options ?? new LogProcessingOptions();

            // 配置 JSON 序列化選項
            _jsonOptions = new JsonSerializerOptions
            {
                WriteIndented = false,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull,
                MaxDepth = _options.MaxSerializationDepth
            };
        }

        #endregion

        #region Public Methods

        /// <summary> 處理實體變更記錄 </summary>
        public SimpleLogEntry ProcessEntityChange(EntityChangeRecord record)
        {
            try
            {
                var startTime = DateTime.UtcNow;
                var operation = record.Operation.ToString().ToUpper();

                var logEntry = new SimpleLogEntry(operation, record.EntityType, record.EntityId, 
                    GenerateChangeSummary(operation, record.EntityType, record.ChangedProperties))
                {
                    Level = LogLevels.Information,
                    Source = "EntityFramework",
                    UserId = record.UserId
                };

                // 處理資料內容
                var logData = new LogData(operation, null, null, record.ChangedProperties);

                switch (record.Operation)
                {
                    case EntityChangeOperation.Create:
                        logData.AfterData = SafeSerialize(record.AfterValues);
                        logData.Summary = $"新增 {record.EntityType}，包含 {record.AfterValues?.Count ?? 0} 個欄位";
                        break;

                    case EntityChangeOperation.Update:
                        logData.BeforeData = SafeSerialize(record.BeforeValues);
                        logData.AfterData = SafeSerialize(record.AfterValues);
                        logData.ChangedFields = record.ChangedProperties;
                        logData.Summary = $"修改 {record.EntityType}，變更 {record.ChangedProperties.Count} 個欄位";
                        break;

                    case EntityChangeOperation.Delete:
                        logData.BeforeData = SafeSerialize(record.BeforeValues);
                        logData.Summary = $"刪除 {record.EntityType}，記錄 {record.BeforeValues?.Count ?? 0} 個欄位";
                        break;
                }

                logData.SetProcessingTime(startTime);
                logEntry.Data = logData;

                return logEntry;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "處理實體變更記錄時發生錯誤: {EntityType}", record.EntityType);
                return CreateErrorLogEntry($"處理實體變更失敗: {ex.Message}", record.EntityType);
            }
        }

        /// <summary> 處理簡單日誌 </summary>
        public SimpleLogEntry ProcessSimpleLog(string level, string message, string source, Exception? exception = null)
        {
            var logEntry = new SimpleLogEntry(level, message, source);

            if (exception != null)
            {
                logEntry.ErrorMessage = exception.Message;
                logEntry.StackTrace = exception.StackTrace;

                // 創建錯誤資料
                logEntry.Data = new LogData
                {
                    Operation = OperationTypes.Error,
                    Summary = $"系統錯誤: {exception.GetType().Name}",
                    Status = "Error"
                };

                logEntry.Data.AddMetadata("exceptionType", exception.GetType().Name);
                if (exception.InnerException != null)
                {
                    logEntry.Data.AddMetadata("innerException", exception.InnerException.Message);
                }
            }

            return logEntry;
        }

        /// <summary> 安全序列化物件 </summary>
        public string SafeSerialize(object? data)
        {
            if (data == null) return "null";

            try
            {
                // 如果是字典，直接序列化
                if (data is Dictionary<string, object> dict)
                {
                    var safeDict = FilterSensitiveData(dict);
                    return JsonSerializer.Serialize(safeDict, _jsonOptions);
                }

                // 提取安全屬性後序列化
                var safeProperties = ExtractSafeProperties(data);
                var filteredProperties = FilterSensitiveData(safeProperties);
                return JsonSerializer.Serialize(filteredProperties, _jsonOptions);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "序列化物件時發生錯誤");
                return $"{{\"serializationError\": \"{ex.Message}\", \"objectType\": \"{data.GetType().Name}\"}}";
            }
        }

        /// <summary> 處理批次變更 </summary>
        public List<SimpleLogEntry> ProcessBatchChanges(EntityChangeSnapshot snapshot, string transactionId)
        {
            var logEntries = new List<SimpleLogEntry>();

            try
            {
                // 處理個別變更
                foreach (var change in snapshot.Changes)
                {
                    var logEntry = ProcessEntityChange(change);
                    logEntry.TransactionId = transactionId;
                    logEntries.Add(logEntry);
                }

                // 如果有多個變更，創建批次摘要
                if (snapshot.Changes.Count > 1)
                {
                    var batchSummary = CreateBatchSummary(snapshot, transactionId);
                    logEntries.Add(batchSummary);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "處理批次變更時發生錯誤");
                var errorEntry = CreateErrorLogEntry($"批次處理失敗: {ex.Message}", "Batch");
                errorEntry.TransactionId = transactionId;
                logEntries.Add(errorEntry);
            }

            return logEntries;
        }

        /// <summary> 提取安全屬性 </summary>
        public Dictionary<string, object> ExtractSafeProperties(object entity)
        {
            var result = new Dictionary<string, object>();

            if (entity == null) return result;

            try
            {
                var entityType = entity.GetType();
                var properties = entityType.GetProperties(BindingFlags.Public | BindingFlags.Instance);

                foreach (var property in properties)
                {
                    try
                    {
                        // 跳過索引器和導航屬性
                        if (property.GetIndexParameters().Length > 0 || IsNavigationProperty(property))
                        {
                            continue;
                        }

                        // 只處理安全類型
                        if (!IsSafeType(property.PropertyType))
                        {
                            continue;
                        }

                        var value = property.GetValue(entity);
                        if (value != null)
                        {
                            result[property.Name] = ConvertToSafeValue(value);
                        }
                    }
                    catch (Exception ex)
                    {
                        result[property.Name] = $"[讀取錯誤: {ex.Message}]";
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "提取安全屬性時發生錯誤");
                result["extractionError"] = ex.Message;
            }

            return result;
        }

        /// <summary> 計算欄位差異 </summary>
        public List<string> CalculateChangedFields(Dictionary<string, object>? beforeData, Dictionary<string, object>? afterData)
        {
            var changedFields = new List<string>();

            if (afterData == null) return changedFields;

            foreach (var kvp in afterData)
            {
                var beforeValue = beforeData?.GetValueOrDefault(kvp.Key);
                if (!Equals(beforeValue, kvp.Value))
                {
                    changedFields.Add(kvp.Key);
                }
            }

            return changedFields;
        }

        /// <summary> 生成變更摘要 </summary>
        public string GenerateChangeSummary(string operation, string entityType, List<string> changedFields)
        {
            return operation switch
            {
                "CREATE" => $"新增 {entityType} 實體",
                "UPDATE" => $"修改 {entityType} 實體，變更欄位: {string.Join(", ", changedFields)}",
                "DELETE" => $"刪除 {entityType} 實體",
                _ => $"處理 {entityType} 實體 ({operation})"
            };
        }

        #endregion

        #region Private Methods

        /// <summary> 過濾敏感資料 </summary>
        private Dictionary<string, object> FilterSensitiveData(Dictionary<string, object> data)
        {
            if (!_options.IncludeSensitiveData)
            {
                var filtered = new Dictionary<string, object>();
                foreach (var kvp in data)
                {
                    if (_options.SensitiveFields.Any(field => 
                        kvp.Key.Contains(field, StringComparison.OrdinalIgnoreCase)))
                    {
                        filtered[kvp.Key] = "[敏感資料已隱藏]";
                    }
                    else
                    {
                        filtered[kvp.Key] = kvp.Value;
                    }
                }
                return filtered;
            }

            return data;
        }

        /// <summary> 檢查是否為安全類型 </summary>
        private bool IsSafeType(Type type)
        {
            var underlyingType = Nullable.GetUnderlyingType(type) ?? type;
            return _safeTypes.Contains(type) || _safeTypes.Contains(underlyingType) || underlyingType.IsEnum;
        }

        /// <summary> 檢查是否為導航屬性 </summary>
        private bool IsNavigationProperty(PropertyInfo property)
        {
            // 檢查是否有 EF Core 導航屬性特徵
            return property.PropertyType.IsClass && 
                   property.PropertyType != typeof(string) && 
                   property.PropertyType != typeof(byte[]) &&
                   !IsSafeType(property.PropertyType);
        }

        /// <summary> 轉換為安全值 </summary>
        private object ConvertToSafeValue(object value)
        {
            return value switch
            {
                Guid guid => guid.ToString(),
                DateTime dateTime => dateTime.ToString("yyyy-MM-dd HH:mm:ss"),
                DateTimeOffset dateTimeOffset => dateTimeOffset.ToString("yyyy-MM-dd HH:mm:ss zzz"),
                _ => value
            };
        }

        /// <summary> 創建錯誤日誌記錄 </summary>
        private SimpleLogEntry CreateErrorLogEntry(string errorMessage, string entityType)
        {
            return new SimpleLogEntry(LogLevels.Error, errorMessage, "LogDataProcessor")
            {
                EntityType = entityType,
                Operation = OperationTypes.Error,
                ErrorMessage = errorMessage,
                Data = new LogData
                {
                    Operation = OperationTypes.Error,
                    Summary = errorMessage,
                    Status = "Error"
                }
            };
        }

        /// <summary> 創建批次摘要 </summary>
        private SimpleLogEntry CreateBatchSummary(EntityChangeSnapshot snapshot, string transactionId)
        {
            var operationCounts = snapshot.Changes
                .GroupBy(c => c.Operation.ToString().ToUpper())
                .ToDictionary(g => g.Key, g => g.Count());

            var summary = $"批次操作包含 {snapshot.TotalChanges} 個變更：" +
                         string.Join("、", operationCounts.Select(kv => $"{kv.Value}個{GetOperationDisplayName(kv.Key)}"));

            var batchEntry = new SimpleLogEntry(OperationTypes.Batch, summary, "EntityFramework")
            {
                TransactionId = transactionId,
                Data = new LogData
                {
                    Operation = OperationTypes.Batch,
                    Summary = summary
                }
            };

            // 添加批次統計資訊
            foreach (var kvp in operationCounts)
            {
                batchEntry.Data.AddMetadata($"{kvp.Key.ToLower()}Count", kvp.Value.ToString());
            }

            batchEntry.Data.AddMetadata("totalChanges", snapshot.TotalChanges.ToString());
            batchEntry.Data.AddMetadata("captureTime", snapshot.CaptureTime.ToString("yyyy-MM-dd HH:mm:ss"));

            return batchEntry;
        }

        /// <summary> 獲取操作顯示名稱 </summary>
        private string GetOperationDisplayName(string operation)
        {
            return operation switch
            {
                "CREATE" => "新增",
                "UPDATE" => "修改",
                "DELETE" => "刪除",
                _ => operation
            };
        }

        #endregion
    }
}
